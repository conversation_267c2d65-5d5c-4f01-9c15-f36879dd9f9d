package com.isxcode.spark.plugin.container.sql;

import com.alibaba.fastjson.JSON;
import com.isxcode.spark.api.agent.req.ContainerGetDataReq;
import com.isxcode.spark.api.agent.req.PluginReq;
import com.isxcode.spark.api.agent.res.ContainerCheckRes;
import com.isxcode.spark.api.agent.res.ExecuteContainerSqlRes;
import org.apache.logging.log4j.util.Strings;
import org.apache.spark.SparkConf;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import spark.Spark;

import java.util.*;

import static spark.Spark.port;
import static spark.Spark.post;

public class Execute {

    public static void main(String[] args) {

        // 解析插件请求
        PluginReq pluginReq = parse(args);

        // 初始化sparkSession
        SparkSession sparkSession = initSparkSession(pluginReq);

        // 使用jetty，创建一个小型的接口服务器
        port(pluginReq.getContainerPort());

        // 执行sql接口
        post("/getData", "application/json", (req, res) -> {

            // 解析请求对象
            ContainerGetDataReq containerGetDataReq = JSON.parseObject(req.body(), ContainerGetDataReq.class);

            // 过滤sql注释
            String regex = "/\\*(?:.|[\\n\\r])*?\\*/|--.*";
            String noCommentSql = containerGetDataReq.getSql().replaceAll(regex, "");
            String realSql = noCommentSql.replace("\n", " ");
            String[] sqls = realSql.split(";");

            // 先选择db
            try {
                sparkSession.sql("use " + pluginReq.getDatabase());
            } catch (Exception e) {
                res.status(500);
                return e.getMessage();
            }

            // 执行前面的sql
            for (int i = 0; i < sqls.length - 1; i++) {
                if (!Strings.isEmpty(sqls[i])) {
                    try {
                        sparkSession.sql(sqls[i]);
                    } catch (Exception e) {
                        res.status(500);
                        return e.getMessage();
                    }
                }
            }

            // 查询最后一句sql
            try {
                Dataset<Row> rowDataset = sparkSession.sql(sqls[sqls.length - 1]).limit(pluginReq.getLimit());
                // 返回结果
                res.type("application/json");
                return exportResult(rowDataset);
            } catch (Exception e) {
                res.status(500);
                return e.getMessage();
            }
        });

        // 检测接口，判断是否可以访问
        Spark.get("/check", (req, res) -> {
            res.type("application/json");
            return JSON.toJSONString(ContainerCheckRes.builder().code("200").build());
        });

        // 初始化sparkjava
        Spark.awaitInitialization();

        // 死循环，防止yarn中止进程
        while (true) {
            try {
                Thread.sleep(100000);
            } catch (InterruptedException e) {
                System.out.println("异步循环中止");
            }
        }
    }

    /**
     * 解析插件请求.
     */
    public static PluginReq parse(String[] args) {
        if (args.length == 0) {
            throw new RuntimeException("args is empty");
        }
        return JSON.parseObject(Base64.getDecoder().decode(args[0]), PluginReq.class);
    }

    /**
     * 初始化sparkSession.
     */
    public static SparkSession initSparkSession(PluginReq pluginReq) {

        SparkSession.Builder sparkSessionBuilder = SparkSession.builder();

        SparkConf conf = new SparkConf();
        if (pluginReq.getSparkConfig() != null) {
            for (Map.Entry<String, String> entry : pluginReq.getSparkConfig().entrySet()) {
                conf.set(entry.getKey(), entry.getValue());
            }
        }

        return sparkSessionBuilder.config(conf).enableHiveSupport().getOrCreate();
    }

    /**
     * 导出数据.
     */
    public static String exportResult(Dataset<Row> rowDataset) {

        // 返回结构
        List<List<String>> result = new ArrayList<>();

        // 分析表头
        result.add(Arrays.asList(rowDataset.columns()));

        // 分析数据
        rowDataset.collectAsList().forEach(e -> {
            List<String> metaData = new ArrayList<>();
            for (int i = 0; i < e.size(); i++) {
                metaData.add(String.valueOf(e.get(i)));
            }
            result.add(metaData);
        });

        // 封装返回对象
        return JSON.toJSONString(ExecuteContainerSqlRes.builder().data(result).code("200").build());
    }
}
