<template>
  <div>
    <!-- 顶部loading条 -->
    <LoadingTopLoadingBar ref="topLoadingRef" />
    
    <!-- 页面内容 -->
    <NuxtPage />
  </div>
</template>

<script setup lang="ts">
const topLoadingRef = ref();
const { setInstance, start, finish } = useTopLoading();

// 监听路由变化
const route = useRoute();
const router = useRouter();

// 设置loading实例
onMounted(() => {
  if (topLoadingRef.value) {
    setInstance(topLoadingRef.value);
  }
});

// 监听路由开始变化
router.beforeEach((to, from) => {
  // 如果是不同的路由，显示loading
  if (to.path !== from.path) {
    start();
  }
});

// 监听路由变化完成
router.afterEach(() => {
  // 延迟一点时间让页面渲染完成
  setTimeout(() => {
    finish();
  }, 100);
});

// 监听页面加载状态
onMounted(() => {
  // 监听资源加载
  const handleLoad = () => {
    finish();
  };
  
  const handleError = () => {
    const { error } = useTopLoading();
    error();
  };

  // 如果页面还在加载中，显示loading
  if (document.readyState === 'loading') {
    start();
    document.addEventListener('DOMContentLoaded', handleLoad);
  }
  
  // 监听图片和其他资源加载
  window.addEventListener('load', handleLoad);
  window.addEventListener('error', handleError, true);
  
  // 清理事件监听器
  onBeforeUnmount(() => {
    document.removeEventListener('DOMContentLoaded', handleLoad);
    window.removeEventListener('load', handleLoad);
    window.removeEventListener('error', handleError, true);
  });
});
</script>
