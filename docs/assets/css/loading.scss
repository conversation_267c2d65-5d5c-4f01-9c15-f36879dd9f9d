/* 顶部loading条样式 */
.top-loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  z-index: 9999;
  background: transparent;
  pointer-events: none;
  
  &__progress {
    height: 100%;
    background: linear-gradient(90deg, #e25a1b 0%, #ff7b3d 50%, #e25a1b 100%);
    background-size: 200% 100%;
    width: 0%;
    transition: width 0.3s ease;
    animation: shimmer 1.5s infinite;
    box-shadow: 0 0 8px rgba(226, 90, 27, 0.6);
  }
  
  &--loading {
    .top-loading-bar__progress {
      animation: shimmer 1.5s infinite, loading-pulse 2s infinite;
    }
  }
  
  &--complete {
    .top-loading-bar__progress {
      width: 100% !important;
      transition: width 0.2s ease;
    }
  }
  
  &--error {
    .top-loading-bar__progress {
      background: linear-gradient(90deg, #f56565 0%, #fc8181 50%, #f56565 100%);
      background-size: 200% 100%;
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-loading-bar {
    height: 1.5px;
  }
}
