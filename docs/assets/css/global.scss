// 全局样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: "阿里巴巴普惠体 2.0 45 Light", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 链接样式
a {
  color: #e25a1b;
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: #ff7b3d;
  }
}

// 图片响应式
img {
  max-width: 100%;
  height: auto;
}

// 清除浮动
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}
