/* Markdown 通用样式 */
.markdown-content {
  max-width: none;
  padding: 20px;
}

.markdown-content img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.markdown-content pre {
  position: relative;
}

.markdown-content pre code {
  font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
}

/* 代码块复制按钮样式 */
.copy-code-button {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.markdown-content pre:hover .copy-code-button {
  opacity: 1;
}

.copy-code-button:hover {
  background: rgba(255, 255, 255, 0.9);
}
