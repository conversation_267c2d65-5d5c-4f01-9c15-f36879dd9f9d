package com.isxcode.spark.license.repository;

import com.isxcode.spark.license.entity.LicenseEntity;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@CacheConfig(cacheNames = {"SY_LICENSE"})
public interface LicenseRepository extends JpaRepository<LicenseEntity, String> {

    Optional<LicenseEntity> findByCode(String code);

    Optional<LicenseEntity> findByStatus(String status);

    @Query("SELECT L FROM LicenseEntity L " + "WHERE L.companyName LIKE %:keyword% "
        + "OR L.remark LIKE %:keyword% order by <PERSON>.createDateTime desc ")
    Page<LicenseEntity> searchAll(@Param("keyword") String searchKeyWord, Pageable pageable);
}
