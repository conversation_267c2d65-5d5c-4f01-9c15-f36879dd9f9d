package com.isxcode.spark.license.service;

import com.alibaba.fastjson2.JSON;
import com.isxcode.spark.api.license.constants.LicenseStatus;
import com.isxcode.spark.api.license.req.QueryLicenseReq;
import com.isxcode.spark.api.license.req.LicenseReq;
import com.isxcode.spark.api.license.res.QueryLicenseRes;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.path.PathUtils;
import com.isxcode.spark.license.entity.LicenseEntity;
import com.isxcode.spark.license.mapper.LicenseMapper;
import com.isxcode.spark.license.repository.LicenseRepository;
import com.isxcode.spark.modules.license.repository.LicenseStore;
import global.namespace.fun.io.bios.BIOS;
import global.namespace.truelicense.api.License;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Base64;
import java.util.Optional;
import java.util.UUID;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class LicenseService {

    private final IsxAppProperties isxAppProperties;

    private final LicenseMapper licenseMapper;

    private final LicenseRepository licenseRepository;

    private final LicenseStore licenseStore;

    public void uploadLicense(MultipartFile licenseFile) {

        // 判断license文件夹是否存在
        if (!new File(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
            .exists()) {
            try {
                Files.createDirectories(Paths
                    .get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license"));
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }

        // 初始化文件名称
        String licenseTmpFileName = UUID.randomUUID().toString();

        // 先把许可证保存下来
        try (InputStream inputStream = licenseFile.getInputStream()) {
            Files.copy(inputStream,
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseTmpFileName),
                StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("上传许可证失败");
        }

        // 校验合法行
        License license;
        try {
            LicenseManager licenseManager = new LicenseManager();
            licenseManager.install(BIOS.file(
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseTmpFileName)));
            licenseManager.verify();
            license = licenseManager.load();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            try {
                // 检验不合法，删除无效许可证
                Files.delete(Paths
                    .get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseTmpFileName));
            } catch (IOException ex) {
                log.error(e.getMessage(), ex);
                throw new IsxAppException("删除无效许可证异常");
            }
            if (e.getMessage().contains("License validity period has expired at")) {
                throw new IsxAppException("证书已过期");
            } else if (e.getMessage().contains("Invalid license management subject")) {
                throw new IsxAppException("证书无法通过验证");
            } else {
                throw new IsxAppException(e.getMessage());
            }
        }

        // 获取许可证中的信息
        LicenseReq licenseReq =
            JSON.parseObject(new String(Base64.getDecoder().decode(license.getInfo())), LicenseReq.class);

        // 判断许可证重复上传
        Optional<LicenseEntity> licenseEntityOptional = licenseRepository.findByCode(licenseReq.getCode());
        if (licenseEntityOptional.isPresent()) {
            throw new IsxAppException("证书已存在");
        }

        // 将数据转成entity对象
        LicenseEntity licenseEntity = licenseMapper.licenseReqToLicenseEntity(licenseReq);
        licenseEntity.setStatus(LicenseStatus.DISABLE);
        licenseEntity.setIssuer(String.valueOf(license.getIssuer()));

        // 将临时许可证改名为许可证编码
        try {
            Files.copy(
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseTmpFileName),
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseReq.getCode() + ".lic"),
                StandardCopyOption.REPLACE_EXISTING);
            Files.delete(
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseTmpFileName));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("证书保存异常");
        }

        // 将证书信息保存到数据库D
        licenseEntity = licenseRepository.save(licenseEntity);

        // 如果用户没有认证的话，自动启用
        Optional<LicenseEntity> byStatus = licenseRepository.findByStatus(LicenseStatus.ENABLE);
        if (licenseStore.getLicense() == null && !byStatus.isPresent()) {
            licenseStore.setLicense(licenseReq);
            licenseEntity.setStatus(LicenseStatus.ENABLE);
            licenseRepository.save(licenseEntity);
        }
    }

    public void enableLicense(String licenseId) {

        // 查看许可证是否存在
        Optional<LicenseEntity> licenseEntityOptional = licenseRepository.findById(licenseId);
        if (!licenseEntityOptional.isPresent()) {
            throw new IsxAppException("许可证不存在");
        }

        // 获取之前的许可证并禁用
        Optional<LicenseEntity> oldLicenseEntityOptional = licenseRepository.findByStatus(LicenseStatus.ENABLE);

        // 许可证中获取信息
        License license;
        try {
            LicenseManager licenseManager = new LicenseManager();
            licenseManager.install(BIOS.file(
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseEntityOptional.get().getCode() + ".lic")));
            licenseManager.verify();
            license = licenseManager.load();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException(e.getMessage());
        }

        final LicenseReq licenseReq =
            JSON.parseObject(new String(Base64.getDecoder().decode(license.getInfo())), LicenseReq.class);

        // 修改旧许可证状态
        if (oldLicenseEntityOptional.isPresent()) {
            LicenseEntity oldLicenseEntity = oldLicenseEntityOptional.get();
            oldLicenseEntity.setStatus(LicenseStatus.DISABLE);
            licenseRepository.save(oldLicenseEntity);
        }

        // 修改新的许可证状态
        LicenseEntity newLicenseEntity = licenseEntityOptional.get();
        newLicenseEntity.setStatus(LicenseStatus.ENABLE);
        licenseRepository.save(newLicenseEntity);

        // 启用新的许可证
        licenseStore.setLicense(licenseReq);
    }

    public void disableLicense(String licenseId) {

        // 查看许可证是否存在
        LicenseEntity license = licenseRepository.findById(licenseId).orElseThrow(() -> new IsxAppException("许可证不存在"));

        license.setStatus(LicenseStatus.DISABLE);
        licenseRepository.save(license);

        // 清除缓存
        licenseStore.clearLicense();
    }

    public void deleteLicense(String licenseId) {

        // 查看许可证是否存在
        LicenseEntity licenseEntity =
            licenseRepository.findById(licenseId).orElseThrow(() -> new IsxAppException("许可证不存在"));

        // 启用中的证书不可删除
        if (LicenseStatus.ENABLE.equals(licenseEntity.getStatus())) {
            throw new IsxAppException("证书启用中");
        }

        // 删除真实证书
        try {
            Files.delete(
                Paths.get(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()) + File.separator + "license")
                    .resolve(licenseEntity.getCode() + ".lic"));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            log.error("证书不存在，删除证书失败");
        }

        // 删除许可证
        licenseRepository.deleteById(licenseId);

        // 清除缓存
        licenseStore.clearLicense();
    }

    public Page<QueryLicenseRes> queryLicense(QueryLicenseReq licQueryLicenseReq) {

        Page<LicenseEntity> datasourceEntityPage = licenseRepository.searchAll(licQueryLicenseReq.getSearchKeyWord(),
            PageRequest.of(licQueryLicenseReq.getPage(), licQueryLicenseReq.getPageSize()));

        return datasourceEntityPage.map(licenseMapper::licenseEntityToLicQueryLicenseRes);
    }
}
