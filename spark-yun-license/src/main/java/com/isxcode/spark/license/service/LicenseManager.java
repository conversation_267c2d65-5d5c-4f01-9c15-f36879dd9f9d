package com.isxcode.spark.license.service;

import com.isxcode.spark.license.entity.LicenseEntity;
import global.namespace.fun.io.api.Source;
import global.namespace.truelicense.api.ConsumerLicenseManager;
import global.namespace.truelicense.api.License;
import global.namespace.truelicense.api.LicenseManagementContext;
import global.namespace.truelicense.api.LicenseManagementException;
import global.namespace.truelicense.api.LicenseManagerParameters;
import global.namespace.truelicense.api.passwd.PasswordProtection;
import global.namespace.truelicense.core.passwd.ObfuscatedPasswordProtection;
import global.namespace.truelicense.obfuscate.ObfuscatedString;
import global.namespace.truelicense.v4.V4;

public class LicenseManager implements ConsumerLicenseManager {

    private ConsumerLicenseManager licenseManager;

    public LicenseManager() {

        LicenseManagementContext licenseManagementContext = V4.builder().subject("spark-yun").build();

        this.licenseManager = licenseManagementContext.consumer().encryption()
            .protection(protection(new long[] {0x89693aa556027f32L, 0x650315939ed131ddL, 0xa3f4f3236f34abe9L})).up()
            .authentication().alias("standard").loadFromResource("public.ks")
            .storeProtection(protection(new long[] {0x8a72a399babf4f84L, 0xb78df6747b3be4f4L, 0xfb18f9a4b1159c5eL}))
            .up().storeInUserPreferences(LicenseEntity.class).build();
    }

    private static PasswordProtection protection(long[] obfuscated) {
        return new ObfuscatedPasswordProtection(new ObfuscatedString(obfuscated));
    }

    @Override
    public void install(Source source) throws LicenseManagementException {
        licenseManager.install(source);
    }

    @Override
    public License load() throws LicenseManagementException {
        return licenseManager.load();
    }

    @Override
    public void verify() throws LicenseManagementException {
        licenseManager.verify();
    }

    @Override
    public void uninstall() throws LicenseManagementException {
        licenseManager.uninstall();
    }

    @Override
    public LicenseManagerParameters parameters() {
        return licenseManager.parameters();
    }
}
