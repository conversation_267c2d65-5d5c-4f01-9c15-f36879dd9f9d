package com.isxcode.spark.license.config;

import com.alibaba.fastjson2.JSON;
import com.isxcode.spark.api.license.constants.LicenseStatus;
import com.isxcode.spark.api.license.req.LicenseReq;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.path.PathUtils;
import com.isxcode.spark.license.entity.LicenseEntity;
import com.isxcode.spark.license.repository.LicenseRepository;
import com.isxcode.spark.license.service.LicenseManager;
import com.isxcode.spark.modules.license.repository.LicenseStore;
import global.namespace.fun.io.bios.BIOS;
import global.namespace.truelicense.api.License;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

@Slf4j
@Configuration
@RequiredArgsConstructor
@Service
public class LicenseConfig {

    private final LicenseRepository licenseRepository;

    private final IsxAppProperties isxAppProperties;

    private final LicenseStore licenseStore;

    @Bean
    public void initGlobalLicense() {

        // 获取证书列表，获取有激活状态的证书
        LicenseEntity licenseEntity;
        try {
            Optional<LicenseEntity> licenseEntityOptional = licenseRepository.findByStatus(LicenseStatus.ENABLE);
            if (!licenseEntityOptional.isPresent()) {
                return;
            }
            licenseEntity = licenseEntityOptional.get();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return;
        }

        // 许可证中获取信息
        License license;
        try {
            LicenseManager licenseManager = new LicenseManager();
            Path path =
                Paths.get(PathUtils.parseProjectPath(PathUtils.parseProjectPath(isxAppProperties.getResourcesPath()))
                    + File.separator + "license").resolve(licenseEntity.getCode() + ".lic");
            BIOS.FileStore file = BIOS.file(path);
            if (!file.exists()) {
                log.info("路径:" + path + "下许可证不存在，请确认许可证是否已被删除");
                return;
            }
            licenseManager.install(file);
            licenseManager.verify();
            license = licenseManager.load();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return;
        }

        licenseStore
            .setLicense(JSON.parseObject(new String(Base64.getDecoder().decode(license.getInfo())), LicenseReq.class));
    }
}
