package com.isxcode.spark.license.mapper;

import com.isxcode.spark.api.license.req.LicenseReq;
import com.isxcode.spark.api.license.res.QueryLicenseRes;
import com.isxcode.spark.license.entity.LicenseEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface LicenseMapper {

    LicenseEntity licenseReqToLicenseEntity(LicenseReq licenseReq);

    @Mapping(target = "startDateTime", dateFormat = "yyyy-MM-dd")
    @Mapping(target = "endDateTime", dateFormat = "yyyy-MM-dd")
    QueryLicenseRes licenseEntityToLicQueryLicenseRes(LicenseEntity licenseEntity);
}
