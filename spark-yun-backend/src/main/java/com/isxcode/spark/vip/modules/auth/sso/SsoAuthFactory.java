package com.isxcode.spark.vip.modules.auth.sso;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
public class SsoAuthFactory {

    private final ApplicationContext applicationContext;

    public SsoAuth getOssAuth(String ssoType) {

        return applicationContext.getBeansOfType(SsoAuth.class).values().stream()
            .filter(agent -> agent.getSsoType().equals(ssoType)).findFirst()
            .orElseThrow(() -> new IsxAppException("单点模式不支持"));
    }
}
