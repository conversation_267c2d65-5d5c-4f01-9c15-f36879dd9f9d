package com.isxcode.spark.vip.modules.meta.service;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.meta.entity.MetaInstanceEntity;
import com.isxcode.spark.modules.meta.entity.MetaWorkEntity;
import com.isxcode.spark.modules.meta.repository.MetaInstanceRepository;
import com.isxcode.spark.modules.meta.repository.MetaWorkRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MetaService {

    private final MetaWorkRepository metaWorkRepository;

    private final MetaInstanceRepository metaInstanceRepository;

    public MetaWorkEntity getMetaWork(String metaWorkId) {

        return metaWorkRepository.findById(metaWorkId).orElseThrow(() -> new IsxAppException("采集任务不存在"));
    }

    public String getMetaWorkName(String metaWorkId) {

        MetaWorkEntity metaWorkEntity = metaWorkRepository.findById(metaWorkId).orElse(null);
        return metaWorkEntity == null ? metaWorkId : metaWorkEntity.getName();
    }

    public MetaInstanceEntity getMetaWorkInstance(String metaWorkInstanceId) {

        return metaInstanceRepository.findById(metaWorkInstanceId).orElseThrow(() -> new IsxAppException("采集实例不存在"));
    }
}
