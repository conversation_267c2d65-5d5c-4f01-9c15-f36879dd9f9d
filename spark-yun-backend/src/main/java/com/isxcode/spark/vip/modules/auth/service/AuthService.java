package com.isxcode.spark.vip.modules.api.service;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.auth.entity.AuthEntity;
import com.isxcode.spark.modules.auth.repository.AuthRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class AuthService {

    private final AuthRepository authRepository;

    public AuthEntity getSsoAuth(String ssoAuthId) {

        return authRepository.findById(ssoAuthId).orElseThrow(() -> new IsxAppException("单点配置不存在"));
    }
}
