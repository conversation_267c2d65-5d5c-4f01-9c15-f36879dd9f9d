package com.isxcode.spark.vip.modules.auth.sso.impl;

import com.alibaba.fastjson.JSONPath;
import com.isxcode.spark.api.auth.constants.OssType;
import com.isxcode.spark.common.utils.http.HttpUtils;
import com.isxcode.spark.modules.auth.entity.AuthEntity;
import com.isxcode.spark.vip.modules.auth.sso.SsoAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
public class KeycloakSso extends SsoAuth {

    @Override
    public String getSsoType() {
        return OssType.KEYCLOAK;
    }

    @Override
    public String getAccount(AuthEntity authEntity, String code) {

        // 通过code获取token
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        String body = "grant_type=authorization_code&" + "client_id=" + authEntity.getClientId() + "&"
            + "client_secret=" + authEntity.getClientSecret() + "&" + "code=" + code + "&" + "redirect_uri="
            + authEntity.getRedirectUrl();

        HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);
        Map accessTokenMap =
            restTemplate.exchange(authEntity.getAccessTokenUrl(), HttpMethod.POST, requestEntity, Map.class).getBody();

        // 通过token获取用户信息
        Map<String, String> getUserParams = new HashMap<>();
        getUserParams.put("Authorization", "Bearer " + accessTokenMap.get("access_token"));
        String userInfoStr = HttpUtils.doGet(authEntity.getUserUrl(), getUserParams, String.class);
        log.debug("单点获取用户信息:{}", userInfoStr);

        // 通过jsonPath解析用户的账号
        return String.valueOf(JSONPath.read(userInfoStr, authEntity.getAuthJsonPath()));
    }

    @Override
    public String getInvokeUrl(AuthEntity authEntity) {

        // keycloak需要openid权限
        if (Strings.isEmpty(authEntity.getScope())) {
            authEntity.setScope("openid");
        }

        return authEntity.getAuthUrl() + "?client_id=" + authEntity.getClientId() + "&response_type=code&scope="
            + authEntity.getScope() + "&redirect_uri=" + authEntity.getRedirectUrl();

    }
}
