package com.isxcode.spark.vip.modules.api.controller;

import com.isxcode.spark.api.api.req.*;
import com.isxcode.spark.api.api.res.*;
import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.api.service.ApiBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "自定义API模块")
@RestController
@RequestMapping(ModuleVipCode.VIP_API)
@RequiredArgsConstructor
public class ApiController {

    private final ApiBizService apiBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "创建自定义API接口")
    @PostMapping("/addApi")
    @SuccessResponse("添加成功")
    public AddApiRes addApi(@Valid @RequestBody AddApiReq addApiReq) {

        return apiBizService.addApi(addApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新自定义API接口")
    @PostMapping("/updateApi")
    @SuccessResponse("更新成功")
    public UpdateApiRes updateApi(@Valid @RequestBody UpdateApiReq updateApiReq) {

        return apiBizService.updateApi(updateApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页api列表接口")
    @PostMapping("/pageApi")
    @SuccessResponse("查询成功")
    public Page<PageApiRes> pageApi(@Valid @RequestBody PageApiReq pageFormReq) {

        return apiBizService.pageApi(pageFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "发布API接口")
    @PostMapping("/publishApi")
    @SuccessResponse("发布成功")
    public void publishApi(@Valid @RequestBody PublishApiReq publishApiReq) {

        apiBizService.publishApi(publishApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线API接口")
    @PostMapping("/offlineApi")
    @SuccessResponse("下线成功")
    public void offlineApi(@Valid @RequestBody OfflineApiReq offlineApiReq) {

        apiBizService.offlineApi(offlineApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_ADMIN})
    @Operation(summary = "删除API接口")
    @PostMapping("/deleteApi")
    @SuccessResponse("删除成功")
    public void deleteApi(@Valid @RequestBody DeleteApiReq deleteApiReq) {

        apiBizService.deleteApi(deleteApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询单个API信息接口")
    @PostMapping("/getApi")
    @SuccessResponse("查询成功")
    public GetApiRes getApi(@Valid @RequestBody GetApiReq getApiReq) {

        return apiBizService.getApi(getApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "测试api接口")
    @PostMapping("/testApi")
    @SuccessResponse("测试完成")
    public TestApiRes testApi(@Valid @RequestBody TestApiReq testApiReq) {

        return apiBizService.testApi(testApiReq);
    }

}
