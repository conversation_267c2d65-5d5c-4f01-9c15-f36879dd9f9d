package com.isxcode.spark.vip.modules.view.service;

import com.alibaba.fastjson.*;
import com.isxcode.spark.api.datasource.constants.DatasourceType;
import com.isxcode.spark.api.datasource.dto.ConnectInfo;
import com.isxcode.spark.api.view.constants.ViewCardStatus;
import com.isxcode.spark.api.view.constants.ViewCardType;
import com.isxcode.spark.api.view.constants.ViewStatus;
import com.isxcode.spark.api.view.dto.CardInfo;
import com.isxcode.spark.api.view.dto.DataSql;
import com.isxcode.spark.api.view.dto.EchartOption;;
import com.isxcode.spark.api.view.req.*;
import com.isxcode.spark.api.view.res.*;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.jwt.JwtUtils;
import com.isxcode.spark.modules.datasource.entity.DatasourceEntity;
import com.isxcode.spark.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.spark.modules.datasource.service.DatasourceService;
import com.isxcode.spark.modules.datasource.source.DataSourceFactory;
import com.isxcode.spark.modules.datasource.source.Datasource;
import com.isxcode.spark.modules.user.service.UserService;
import com.isxcode.spark.modules.view.entity.ViewLinkEntity;
import com.isxcode.spark.modules.view.repository.ViewLinkRepository;
import com.isxcode.spark.vip.modules.view.data.ViewDataExecutor;
import com.isxcode.spark.modules.view.entity.ViewCardEntity;
import com.isxcode.spark.modules.view.entity.ViewEntity;
import com.isxcode.spark.modules.view.mapper.ViewMapper;
import com.isxcode.spark.modules.view.repository.ViewCardRepository;
import com.isxcode.spark.modules.view.repository.ViewRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.*;

import static com.isxcode.spark.common.config.CommonConfig.JPA_TENANT_MODE;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ViewBizService {

    private final ViewMapper viewMapper;

    private final ViewCardRepository viewCardRepository;

    private final ViewRepository viewRepository;

    private final DatasourceService datasourceService;

    private final ViewService viewService;

    private final ApplicationContext applicationContext;

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceMapper datasourceMapper;

    private final UserService userService;

    private final IsxAppProperties isxAppProperties;

    private final ViewLinkRepository viewLinkRepository;

    public GetViewCardRes addViewCard(AddViewCardReq addViewCardReq) {

        // 判断数据源是否存在
        datasourceService.getDatasource(addViewCardReq.getDatasourceId());

        // 名称不能重复
        viewCardRepository.findByName(addViewCardReq.getName()).ifPresent(viewCardEntity -> {
            throw new IsxAppException("数据卡片名称重复");
        });

        // 转换
        ViewCardEntity viewCardEntity = viewMapper.addViewCardReqToViewCardEntity(addViewCardReq);

        // 初始化样式数据
        viewCardEntity.setExampleData(JSON.toJSONString(getExampleDataSql(addViewCardReq.getType())));

        // 初始化组件状态
        viewCardEntity.setStatus(ViewCardStatus.NEW);

        // 持久化数据
        viewCardRepository.save(viewCardEntity);

        // 返回组件信息
        GetViewCardReq getViewCardReq = GetViewCardReq.builder().id(viewCardEntity.getId()).build();
        return getViewCard(getViewCardReq);
    }

    public static EchartOption getExampleDataSql(String viewCardType) {

        switch (viewCardType) {
            case ViewCardType.PIE:
                return JSON.parseObject("{\n" +
                    "  \"title\": {\n" +
                    "    \"text\": \"饼图\",\n" +
                    "    \"left\": \"center\"\n" +
                    "  },\n" +
                    "  \"tooltip\": {\n" +
                    "    \"trigger\": \"item\"\n" +
                    "  },\n" +
                    "  \"legend\": {\n" +
                    "    \"top\": \"bottom\"\n" +
                    "  },\n" +
                    "  \"series\": [\n" +
                    "    {\n" +
                    "      \"name\": \"Access From\",\n" +
                    "      \"type\": \"pie\",\n" +
                    "      \"radius\": \"50%\",\n" +
                    "      \"data\": [\n" +
                    "        {\n" +
                    "          \"name\": \"Search Engine\",\n" +
                    "          \"value\": 1048\n" +
                    "        },\n" +
                    "        {\n" +
                    "          \"name\": \"Direct\",\n" +
                    "          \"value\": 735\n" +
                    "        },\n" +
                    "        {\n" +
                    "          \"name\": \"Email\",\n" +
                    "          \"value\": 580\n" +
                    "        },\n" +
                    "        {\n" +
                    "          \"name\": \"Union Ads\",\n" +
                    "          \"value\": 484\n" +
                    "        },\n" +
                    "        {\n" +
                    "          \"name\": \"Video Ads\",\n" +
                    "          \"value\": 300\n" +
                    "        }\n" +
                    "      ],\n" +
                    "      \"emphasis\": {\n" +
                    "        \"itemStyle\": {\n" +
                    "          \"shadowBlur\": 10.0,\n" +
                    "          \"shadowOffsetX\": 0.0,\n" +
                    "          \"shadowColor\": \"rgba(0, 0, 0, 0.5)\"\n" +
                    "        }\n" +
                    "      }\n" +
                    "    }\n" +
                    "  ],\n" +
                    "  \"xaxis\": null,\n" +
                    "  \"yaxis\": null\n" +
                    "}", EchartOption.class);
            case ViewCardType.LINE:
                return JSON.parseObject("{\n" +
                    "  \"title\": {\n" +
                    "    \"text\": \"折线图\",\n" +
                    "    \"left\": \"center\"\n" +
                    "  },\n" +
                    "  tooltip: {\n" +
                    "    trigger: 'axis',\n" +
                    "    axisPointer: {\n" +
                    "      type: 'cross'\n" +
                    "    }\n" +
                    "  },\n" +
                    "  \"xAxis\": {\n" +
                    "    \"type\": \"category\",\n" +
                    "    \"data\": [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"]\n" +
                    "  },\n" +
                    "  \"yAxis\": {\n" +
                    "    \"type\": \"value\",\n" +
                    "    axisPointer: {\n" +
                    "      snap: true\n" +
                    "    }\n" +
                    "  },\n" +
                    "  \"series\": [\n" +
                    "    {\n" +
                    "      \"data\": [150, 230, 224, 218, 135, 147, 260],\n" +
                    "      \"type\": \"line\"\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}", EchartOption.class);
            case ViewCardType.BAR:
                return JSON.parseObject("{\n" +
                    "  \"title\": {\n" +
                    "    \"text\": \"柱状图\",\n" +
                    "    \"left\": \"center\"\n" +
                    "  },\n" +
                    "  tooltip: {\n" +
                    "    trigger: 'axis',\n" +
                    "    axisPointer: {\n" +
                    "      type: 'shadow'\n" +
                    "    }\n" +
                    "  },\n" +
                    "  \"xAxis\": {\n" +
                    "    \"type\": \"category\",\n" +
                    "    \"data\": [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"]\n" +
                    "  },\n" +
                    "  \"yAxis\": {\n" +
                    "    \"type\": \"value\",\n" +
                    "  },\n" +
                    "  \"series\": [\n" +
                    "    {\n" +
                    "      \"data\": [120, 200, 150, 80, 70, 110, 130],\n" +
                    "      \"type\": \"bar\"\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}", EchartOption.class);
            default:
                throw new IsxAppException("暂不支持");
        }
    }

    public Page<PageViewCardRes> pageViewCard(PageViewCardReq pageViewCardReq) {

        Page<ViewCardEntity> viewCardEntities = viewCardRepository.pageViewCard(pageViewCardReq.getSearchKeyWord(),
            PageRequest.of(pageViewCardReq.getPage(), pageViewCardReq.getPageSize()));

        Page<PageViewCardRes> res = viewCardEntities.map(viewMapper::viewCardEntityToPageViewCardRes);

        res.getContent().forEach(e -> {
            e.setDatasourceName(datasourceService.getDatasourceName(e.getDatasourceId()));
            e.setCreateUsername(userService.getUserName(e.getCreateBy()));
        });

        return res;
    }

    public void publishViewCard(PublishViewCardReq publishViewCardReq) {

        ViewCardEntity viewCard = viewService.getViewCard(publishViewCardReq.getId());

        viewCard.setStatus(ViewCardStatus.PUBLISHED);
        viewCardRepository.save(viewCard);
    }

    public void offlineViewCard(OfflineViewCardReq offlineViewCardReq) {

        ViewCardEntity viewCard = viewService.getViewCard(offlineViewCardReq.getId());

        viewCard.setStatus(ViewCardStatus.OFFLINE);
        viewCardRepository.save(viewCard);
    }

    public void deleteViewCard(DeleteViewCardReq deleteViewCardReq) {

        ViewCardEntity viewCard = viewService.getViewCard(deleteViewCardReq.getId());

        viewCardRepository.delete(viewCard);
    }

    public GetViewCardRes getViewCard(GetViewCardReq getViewCardReq) {

        CardInfo cardInfo = viewService.getViewCardInfo(getViewCardReq.getId());
        return GetViewCardRes.builder().cardInfo(cardInfo).build();
    }

    public GetViewCardDataRes getViewCardData(GetViewCardDataReq getViewCardDataReq) {

        ViewCardEntity viewCard = viewService.getViewCard(getViewCardDataReq.getId());

        ViewDataExecutor viewDataExecutor = applicationContext.getBeansOfType(ViewDataExecutor.class).values().stream()
            .filter(agent -> agent.getViewType().equals(viewCard.getType())).findFirst().orElseThrow(() -> new IsxAppException("图形类型不支持"));

        // 获取数据
        EchartOption viewData = viewDataExecutor.getViewData(viewCard, getViewCardDataReq.getDataSql());

        // 保存临时数据
        viewCard.setExampleData(JSON.toJSONString(viewData));
        viewCardRepository.save(viewCard);

        return GetViewCardDataRes.builder().viewData(viewData).build();
    }

    public AddViewRes addView(AddViewReq addViewReq) {

        // 名称不能重复
        Optional<ViewEntity> viewEntityOptional = viewRepository.findByName(addViewReq.getName());
        if (viewEntityOptional.isPresent()) {
            throw new IsxAppException("大屏名称重复");
        }

        // 转换
        ViewEntity viewEntity = viewMapper.addViewReqToViewEntity(addViewReq);
        viewEntity.setStatus(ViewStatus.NEW);

        // 持久化数据
        viewEntity = viewRepository.save(viewEntity);

        // 返回AddViewRes
        return viewMapper.viewEntityToAddViewRes(viewEntity);
    }

    public void configViewCard(ConfigViewCardReq configViewCardReq) {

        ViewCardEntity viewCard = viewService.getViewCard(configViewCardReq.getId());

        viewCard.setDatasourceId(configViewCardReq.getDatasourceId());
        viewCard.setDataSql(JSON.toJSONString(configViewCardReq.getDataSql()));
        viewCard.setWebConfig(JSON.toJSONString(configViewCardReq.getWebConfig()));
        viewCard.setExampleData(JSON.toJSONString(configViewCardReq.getExampleData()));
        viewCard.setName(configViewCardReq.getName());

        viewCardRepository.save(viewCard);
    }

    public Page<PageViewRes> pageView(PageViewReq pageViewReq) {

        Page<ViewEntity> viewEntities = viewRepository.pageView(pageViewReq.getSearchKeyWord(),
            PageRequest.of(pageViewReq.getPage(), pageViewReq.getPageSize()));

        Page<PageViewRes> map = viewEntities.map(viewMapper::viewEntityToPageViewRes);

        map.getContent().forEach(e -> e.setCreateUsername(userService.getUserName(e.getCreateBy())));

        return map;
    }

    public void deleteView(DeleteViewReq deleteViewReq) {

        ViewEntity view = viewService.getView(deleteViewReq.getId());

        viewRepository.delete(view);
    }

    public void publishView(PublishViewReq publishViewReq) {

        ViewEntity view = viewService.getView(publishViewReq.getId());

        view.setStatus(ViewCardStatus.PUBLISHED);
        viewRepository.save(view);
    }

    public void offlineView(OfflineViewReq offlineViewReq) {

        ViewEntity view = viewService.getView(offlineViewReq.getId());

        view.setStatus(ViewCardStatus.OFFLINE);
        viewRepository.save(view);
    }

    public void configView(ConfigViewReq configViewReq) {

        ViewEntity view = viewService.getView(configViewReq.getId());

        view.setCardList(JSON.toJSONString(configViewReq.getCardList()));
        view.setWebConfig(JSON.toJSONString(configViewReq.getWebConfig()));

        viewRepository.save(view);
    }

    public GetViewRes getView(GetViewReq getViewReq) {

        ViewEntity view = viewService.getView(getViewReq.getId());

        GetViewRes getViewRes = new GetViewRes();
        getViewRes.setId(view.getId());
        getViewRes.setName(view.getName());
        getViewRes.setStatus(view.getStatus());
        if (!Strings.isEmpty(view.getWebConfig())) {
            getViewRes.setWebConfig(JSON.parseObject(view.getWebConfig()));
        }

        if (!Strings.isEmpty(view.getCardList())) {
            List<String> cardIds = JSONArray.parseArray(view.getCardList(), String.class);
            if (!cardIds.isEmpty()) {
                List<CardInfo> cards = new ArrayList<>();
                cardIds.forEach(e -> {
                    cards.add(viewService.getViewCardInfo(e));
                });
                getViewRes.setCards(cards);
            }
        }

        return getViewRes;
    }

    public GetViewCardDataByIdRes getViewCardDataById(GetViewCardDataByIdReq getViewCardDataByIdReq) {

        ViewCardEntity viewCard = viewService.getViewCard(getViewCardDataByIdReq.getId());

        ViewDataExecutor viewDataExecutor = applicationContext.getBeansOfType(ViewDataExecutor.class).values().stream()
            .filter(agent -> agent.getViewType().equals(viewCard.getType())).findFirst().orElseThrow(() -> new IsxAppException("图形类型不支持"));

        // 获取数据
        EchartOption viewData = viewDataExecutor.getViewData(viewCard, JSON.parseObject(viewCard.getDataSql(), DataSql.class));

        return GetViewCardDataByIdRes.builder().viewData(viewData).build();
    }

    public GetSqlDataRes getSqlData(GetSqlDataReq getSqlDataReq) {

        DatasourceEntity datasourceEntity = datasourceService.getDatasource(getSqlDataReq.getDatasourceId());

        // 检查sql合法性
        if (!DatasourceType.DM.equals(datasourceEntity.getDbType())) {
            try {
                datasourceService.checkSqlValid(getSqlDataReq.getSql());
            } catch (IsxAppException e) {
                throw new IsxAppException("sql语法异常，请检查sql:" + e.getMsg());
            }
        }

        // 检查sql只能查询
        if (!datasourceService.isQueryStatement(getSqlDataReq.getSql())) {
            throw new IsxAppException("只能是查询sql");
        }

        Connection connection;
        try {
            Datasource datasource = dataSourceFactory.getDatasource(datasourceEntity.getDbType());
            ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
            connection = datasource.getConnection(connectInfo);
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(getSqlDataReq.getSql());
            List<String> columns = new ArrayList<>();
            List<List<String>> rows = new ArrayList<>();

            // 封装表头
            int columnCount = resultSet.getMetaData().getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                columns.add(resultSet.getMetaData().getColumnName(i));
            }

            // 封装数据
            while (resultSet.next()) {
                List<String> row = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    try {
                        row.add(resultSet.getString(i));
                    } catch (Exception e) {
                        row.add(String.valueOf(resultSet.getObject(i)));
                    }
                }
                rows.add(row);
            }
            statement.close();
            connection.close();
            return GetSqlDataRes.builder().columns(columns).rows(rows).build();
        } catch (SQLException e) {
            throw new IsxAppException("数据源访问异常," + e.getMessage());
        }
    }

    public GetViewLinkRes getViewLink(GetViewLinkReq getViewLinkReq) {

        // 校验大屏是否存在
        ViewEntity view = viewService.getView(getViewLinkReq.getViewId());

        // 校验表单是否发布
        if (!ViewStatus.PUBLISHED.equals(view.getStatus())) {
            throw new IsxAppException("请先发布大屏");
        }

        // 生成token
        String jwtToken = JwtUtils.encrypt(isxAppProperties.getAesSlat(), "sy_anonymous", isxAppProperties.getJwtKey(),
            getViewLinkReq.getValidDay() * 24 * 60);

        // 生成到期时间
        LocalDateTime invalidDateTime = LocalDateTime.now().plusDays(getViewLinkReq.getValidDay());

        // 封装entity
        ViewLinkEntity viewLink = ViewLinkEntity.builder().viewId(view.getId()).viewVersion("latest")
            .invalidDateTime(invalidDateTime).viewToken(jwtToken).build();

        // 持久化
        viewLink = viewLinkRepository.save(viewLink);

        // 封装返回体
        return GetViewLinkRes.builder().viewLinkId(viewLink.getId()).build();
    }

    public GetViewLinkInfoRes getViewLinkInfo(GetViewLinkInfoReq getViewLinkInfoReq) {

        JPA_TENANT_MODE.set(false);

        // 获取过期的链接
        List<ViewLinkEntity> invalidFormLinkList =
            viewLinkRepository.findAllByInvalidDateTimeBefore(LocalDateTime.now());

        // 删除过期的链接
        viewLinkRepository.deleteAll(invalidFormLinkList);

        // 再查询有效token
        Optional<ViewLinkEntity> viewLinkOptional = viewLinkRepository.findById(getViewLinkInfoReq.getViewLinkId());

        if (!viewLinkOptional.isPresent()) {
            throw new IsxAppException("链接不存在或已过期");
        }

        return viewMapper.viewLinkEntityToGetFormLinkInfoRes(viewLinkOptional.get());
    }

    public void editViewCard(EditViewCardReq editViewCardReq) {

        ViewCardEntity viewCard = viewService.getViewCard(editViewCardReq.getId());

        // 名称不能重复
        viewCardRepository.findByName(editViewCardReq.getName()).ifPresent(viewCardEntity -> {
            if (!viewCardEntity.getId().equals(editViewCardReq.getId())) {
                throw new IsxAppException("数据卡片名称重复");
            }
        });

        viewCard.setName(editViewCardReq.getName());
        viewCard.setRemark(editViewCardReq.getRemark());

        viewCardRepository.save(viewCard);
    }

    public void editView(EditViewReq editViewReq) {

        ViewEntity view = viewService.getView(editViewReq.getId());

        // 名称不能重复
        viewRepository.findByName(editViewReq.getName()).ifPresent(viewEntity -> {
            if (!viewEntity.getId().equals(editViewReq.getId())) {
                throw new IsxAppException("大屏名称重复");
            }
        });

        view.setName(editViewReq.getName());
        view.setRemark(editViewReq.getRemark());

        viewRepository.save(view);
    }
}