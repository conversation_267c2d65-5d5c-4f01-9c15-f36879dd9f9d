package com.isxcode.spark.vip.modules.auth.service;

import com.isxcode.spark.api.auth.constants.AuthStatus;
import com.isxcode.spark.api.auth.req.*;
import com.isxcode.spark.api.auth.res.PageSsoAuthRes;
import com.isxcode.spark.api.tenant.constants.TenantStatus;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.api.user.constants.UserStatus;
import com.isxcode.spark.api.auth.req.SsoLoginReq;
import com.isxcode.spark.api.user.res.LoginRes;
import com.isxcode.spark.api.auth.res.QuerySsoAuthRes;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.jwt.JwtUtils;
import com.isxcode.spark.modules.auth.entity.AuthEntity;
import com.isxcode.spark.modules.auth.mapper.AuthMapper;
import com.isxcode.spark.modules.auth.repository.AuthRepository;
import com.isxcode.spark.modules.tenant.entity.TenantEntity;
import com.isxcode.spark.modules.tenant.repository.TenantRepository;
import com.isxcode.spark.security.user.TenantUserEntity;
import com.isxcode.spark.security.user.TenantUserRepository;
import com.isxcode.spark.security.user.UserEntity;
import com.isxcode.spark.security.user.UserRepository;
import com.isxcode.spark.vip.modules.api.service.AuthService;
import com.isxcode.spark.vip.modules.auth.sso.SsoAuth;
import com.isxcode.spark.vip.modules.auth.sso.SsoAuthFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class AuthBizService {

    private final AuthRepository authRepository;

    private final AuthMapper authMapper;

    private final AuthService authService;

    private final UserRepository userRepository;

    private final IsxAppProperties isxAppProperties;

    private final TenantUserRepository tenantUserRepository;

    private final TenantRepository tenantRepository;

    private final SsoAuthFactory ssoAuthFactory;

    public void addSsoAuth(AddSsoAuthReq addSsoAuthReq) {

        // 名称不能重复
        authRepository.findByName(addSsoAuthReq.getName()).ifPresent(authEntity -> {
            throw new IsxAppException("认证名称重复");
        });

        // clientId唯一
        authRepository.findByClientId(addSsoAuthReq.getClientId()).ifPresent(authEntity -> {
            throw new IsxAppException("clientId必须唯一");
        });

        // 检查回调地址中包含clientId
        if (!addSsoAuthReq.getRedirectUrl().contains("clientId")) {
            throw new IsxAppException("回调地址中需要添加clientId请求");
        }

        AuthEntity authEntity = authMapper.addSsoAuthReqToAuthEntity(addSsoAuthReq);
        authEntity.setStatus(AuthStatus.DISABLE);

        authRepository.save(authEntity);
    }

    public void updateSsoAuth(UpdateSsoAuthReq updateSsoAuthReq) {

        AuthEntity ssoAuth = authService.getSsoAuth(updateSsoAuthReq.getId());

        authRepository.findByName(updateSsoAuthReq.getName()).ifPresent(authEntity -> {
            if (!authEntity.getId().equals(updateSsoAuthReq.getId())) {
                throw new IsxAppException("接口名称重复");
            }
        });

        // clientId唯一
        authRepository.findByClientId(updateSsoAuthReq.getClientId()).ifPresent(authEntity -> {
            if (!authEntity.getId().equals(updateSsoAuthReq.getId())) {
                throw new IsxAppException("clientId必须唯一");
            }
        });

        // 检查回调地址中包含clientId
        if (!updateSsoAuthReq.getRedirectUrl().contains("clientId")) {
            throw new IsxAppException("回调地址中需要添加clientId请求");
        }

        ssoAuth = authMapper.updateSsoAuthReqToAuthEntity(updateSsoAuthReq, ssoAuth);
        authRepository.save(ssoAuth);
    }

    public Page<PageSsoAuthRes> pageSsoAuth(PageSsoAuthReq pageSsoAuthReq) {

        Page<AuthEntity> authEntities = authRepository.pageSsoAuth(pageSsoAuthReq.getSearchKeyWord(),
            PageRequest.of(pageSsoAuthReq.getPage(), pageSsoAuthReq.getPageSize()));

        return authEntities.map(authMapper::AuthEntityToPageSsoAuthRes);
    }

    public void enableSsoAuth(EnableSsoAuthReq enableSsoAuthReq) {

        AuthEntity ssoAuth = authService.getSsoAuth(enableSsoAuthReq.getId());
        ssoAuth.setStatus(AuthStatus.ENABLE);
        authRepository.save(ssoAuth);
    }

    public void disableSsoAuth(DisableSsoAuthReq disableSsoAuthReq) {

        AuthEntity ssoAuth = authService.getSsoAuth(disableSsoAuthReq.getId());
        ssoAuth.setStatus(AuthStatus.DISABLE);
        authRepository.save(ssoAuth);
    }

    public void deleteSsoAuth(DeleteSsoAuthReq deleteSsoAuthReq) {

        AuthEntity ssoAuth = authService.getSsoAuth(deleteSsoAuthReq.getId());
        authRepository.delete(ssoAuth);
    }

    public LoginRes ssoLogin(SsoLoginReq ssoLoginReq) {

        // 获取oss登录配置
        Optional<AuthEntity> authEntityOptional = authRepository.findByClientId(ssoLoginReq.getClientId());
        if (!authEntityOptional.isPresent()) {
            throw new IsxAppException("单点登录配置不存在");
        }
        AuthEntity authEntity = authEntityOptional.get();

        // 检查单点登录是否开启
        if (AuthStatus.DISABLE.equals(authEntity.getStatus())) {
            throw new IsxAppException("单点登录配置已被禁用");
        }

        // 获取账号
        SsoAuth ossAuth = ssoAuthFactory.getOssAuth(authEntity.getSsoType());
        String account = ossAuth.getAccountStr(authEntity, ssoLoginReq.getCode());
        log.debug("单点登录获取的account值:{}", account);

        // 正常登录
        Optional<UserEntity> userEntityOptional = userRepository.findByAccount(account);
        if (!userEntityOptional.isPresent()) {
            throw new IsxAppException("账号或者密码不正确");
        }
        UserEntity userEntity = userEntityOptional.get();

        // 判断用户是否禁用
        if (UserStatus.DISABLE.equals(userEntity.getStatus())) {
            throw new IsxAppException("账号已被禁用，请联系管理员");
        }

        // 生成token
        String jwtToken = JwtUtils.encrypt(isxAppProperties.getAesSlat(), userEntity.getId(),
            isxAppProperties.getJwtKey(), isxAppProperties.getExpirationMin());

        // 如果是系统管理员直接返回
        if (RoleType.SYS_ADMIN.equals(userEntity.getRoleCode())) {
            return LoginRes.builder().tenantId(userEntity.getCurrentTenantId()).username(userEntity.getUsername())
                .phone(userEntity.getPhone()).email(userEntity.getEmail()).remark(userEntity.getRemark())
                .token(jwtToken).role(userEntity.getRoleCode()).build();
        }

        // 如果用户不在任何一个租户报错
        List<TenantUserEntity> tenantUserEntities = tenantUserRepository.findAllByUserId(userEntity.getId());
        if (tenantUserEntities.isEmpty()) {
            throw new IsxAppException("无可用租户，请联系管理员");
        }

        // 如果用户没有任何启动租户报错
        List<String> tenantId =
            tenantUserEntities.stream().map(TenantUserEntity::getTenantId).collect(Collectors.toList());
        List<TenantEntity> enableTenants = tenantRepository.findAllByIdInAndStatus(tenantId, TenantStatus.ENABLE);
        if (enableTenants.isEmpty()) {
            throw new IsxAppException("无启用租户，请联系管理员");
        }

        // 如果用户当前租户id启动则返回当前租户，没有则随机挑一个
        List<String> enableTenantIds = enableTenants.stream().map(TenantEntity::getId).collect(Collectors.toList());
        String currentTenantId;
        if (!Strings.isEmpty(userEntity.getCurrentTenantId())
            && enableTenantIds.contains(userEntity.getCurrentTenantId())) {
            currentTenantId = userEntity.getCurrentTenantId();
        } else {
            currentTenantId = enableTenants.get(0).getId();
        }

        userEntity.setCurrentTenantId(currentTenantId);
        userRepository.save(userEntity);

        // 返回用户在租户中的角色
        Optional<TenantUserEntity> tenantUserEntityOptional =
            tenantUserRepository.findByTenantIdAndUserId(currentTenantId, userEntity.getId());
        if (!tenantUserEntityOptional.isPresent()) {
            throw new IsxAppException("无可用租户，请联系管理员");
        }

        // 生成token并返回
        return new LoginRes(userEntity.getUsername(), userEntity.getPhone(), userEntity.getEmail(),
            userEntity.getRemark(), jwtToken, currentTenantId, tenantUserEntityOptional.get().getRoleCode());
    }

    public GetSsoInvokeUrlRes getSsoInvokeUrl(GetSsoInvokeUrlReq getSsoInvokeUrlReq) {

        AuthEntity authEntity = authService.getSsoAuth(getSsoInvokeUrlReq.getId());
        SsoAuth ossAuth = ssoAuthFactory.getOssAuth(authEntity.getSsoType());
        return GetSsoInvokeUrlRes.builder().invokeUrl(ossAuth.getInvokeUrl(authEntity)).build();
    }

    public List<QuerySsoAuthRes> querySsoAuth() {

        List<QuerySsoAuthRes> result = new ArrayList<>();

        List<AuthEntity> authEntities = authRepository.findAllByStatus(AuthStatus.ENABLE);
        authEntities.forEach(authEntity -> {
            SsoAuth ossAuth = ssoAuthFactory.getOssAuth(authEntity.getSsoType());
            result.add(QuerySsoAuthRes.builder().name(authEntity.getName()).invokeUrl(ossAuth.getInvokeUrl(authEntity))
                .build());
        });

        return result;
    }
}
