package com.isxcode.spark.vip.modules.form.controller;

import com.isxcode.spark.api.form.req.*;
import com.isxcode.spark.api.form.res.*;
import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.form.service.FormBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "自定义表单模块")
@RequestMapping(ModuleVipCode.VIP_FORM)
@RestController
@RequiredArgsConstructor
public class FormController {

    private final FormBizService formBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "创建表单接口")
    @PostMapping("/addForm")
    @SuccessResponse("创建成功")
    public AddFormRes addForm(@Valid @RequestBody AddFormReq addFormReq) {

        return formBizService.addForm(addFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新表单接口")
    @PostMapping("/updateForm")
    @SuccessResponse("更新成功")
    public void updateForm(@Valid @RequestBody UpdateFormReq updateFormReq) {

        formBizService.updateForm(updateFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "保存表单组件接口")
    @PostMapping("/saveFormConfig")
    @SuccessResponse("保存成功")
    public void saveFormConfig(@Valid @RequestBody SaveFormConfigReq saveFormConfigReq) {

        formBizService.saveFormConfig(saveFormConfigReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询表单配置接口")
    @PostMapping("/getFormConfig")
    @SuccessResponse("查询成功")
    public GetFormConfigRes getFormConfig(@Valid @RequestBody GetFormConfigReq getFormConfigReq) {

        return formBizService.getFormConfig(getFormConfigReq);
    }

    @VipApi
    @Secured({RoleType.ROLE_ANONYMOUS})
    @Operation(summary = "匿名者权限查询表单配置接口")
    @PostMapping("/getFormConfigForAnonymous")
    @SuccessResponse("查询成功")
    public GetFormConfigForAnonymousRes getFormConfigForAnonymous(
        @Valid @RequestBody GetFormConfigForAnonymousReq getFormConfigForAnonymousReq) {

        return formBizService.getFormConfigForAnonymous(getFormConfigForAnonymousReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询表单列表接口")
    @PostMapping("/pageForm")
    @SuccessResponse("查询成功")
    public Page<FormPageRes> pageForm(@Valid @RequestBody PageFormReq pageFormReq) {

        return formBizService.pageForm(pageFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN, RoleType.ROLE_ANONYMOUS})
    @Operation(summary = "添加数据接口")
    @PostMapping("/addData")
    @SuccessResponse("添加成功")
    public void addData(@Valid @RequestBody AddDataReq addDataReq) {

        formBizService.addData(addDataReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "删除数据接口")
    @PostMapping("/deleteData")
    @SuccessResponse("删除成功")
    public void deleteData(@Valid @RequestBody DeleteDataReq deleteDataReq) {

        formBizService.deleteData(deleteDataReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新数据接口")
    @PostMapping("/updateData")
    @SuccessResponse("更新成功")
    public void updateData(@Valid @RequestBody UpdateDataReq fomUpdateDataReq) {

        formBizService.updateData(fomUpdateDataReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询表单接口")
    @PostMapping("/pageData")
    @SuccessResponse("查询成功")
    public PageDataRes pageData(@Valid @RequestBody QueryDataReq queryDataReq) {

        return formBizService.pageData(queryDataReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "发布表单接口")
    @PostMapping("/deployForm")
    @SuccessResponse("发布成功")
    public void deployForm(@Valid @RequestBody DeployFormReq deployFormReq) {

        formBizService.deployForm(deployFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线表单接口")
    @PostMapping("/offlineForm")
    @SuccessResponse("下线成功")
    public void offlineForm(@Valid @RequestBody OfflineFormReq offlineFormReq) {

        formBizService.offlineForm(offlineFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线表单接口")
    @PostMapping("/deleteForm")
    @SuccessResponse("删除成功")
    public void deleteForm(@Valid @RequestBody DeleteFormReq deleteFormReq) {

        formBizService.deleteForm(deleteFormReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "获取分享表单链接的id接口")
    @PostMapping("/getFormLink")
    @SuccessResponse("获取成功")
    public GetFormLinkRes getFormLink(@Valid @RequestBody GetFormLinkReq getFormLinkReq) {

        return formBizService.getFormLink(getFormLinkReq);
    }

    @VipApi
    @Operation(summary = "获取分享表单链接的id接口")
    @PostMapping("/open/getFormLinkInfo")
    @SuccessResponse("获取成功")
    public GetFormLinkInfoRes getFormLinkInfo(@Valid @RequestBody GetFormLinkInfoReq getFormLinkInfoReq) {

        return formBizService.getFormLinkInfo(getFormLinkInfoReq);
    }

}
