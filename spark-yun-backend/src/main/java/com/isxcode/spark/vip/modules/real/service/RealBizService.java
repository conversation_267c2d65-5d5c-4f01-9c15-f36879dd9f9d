package com.isxcode.spark.vip.modules.real.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONPath;
import com.isxcode.spark.api.agent.constants.AgentUrl;
import com.isxcode.spark.api.agent.req.GetWorkStatusReq;
import com.isxcode.spark.api.agent.req.GetWorkStderrLogReq;
import com.isxcode.spark.api.agent.req.GetWorkStdoutLogReq;
import com.isxcode.spark.api.agent.req.StopWorkReq;
import com.isxcode.spark.api.agent.res.GetWorkStderrLogRes;
import com.isxcode.spark.api.agent.res.GetWorkStdoutLogRes;
import com.isxcode.spark.api.cluster.constants.ClusterNodeStatus;
import com.isxcode.spark.api.datasource.constants.DatasourceStatus;
import com.isxcode.spark.api.datasource.constants.DatasourceType;
import com.isxcode.spark.api.datasource.dto.KafkaConfig;
import com.isxcode.spark.api.real.constants.RealStatus;
import com.isxcode.spark.api.real.req.*;
import com.isxcode.spark.api.real.res.*;
import com.isxcode.spark.api.work.constants.ResourceLevel;
import com.isxcode.spark.api.work.constants.WorkLog;
import com.isxcode.spark.api.work.dto.SyncWorkConfig;
import com.isxcode.spark.api.work.res.RunWorkRes;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.backend.api.base.pojos.BaseResponse;
import com.isxcode.spark.backend.api.base.properties.IsxAppProperties;
import com.isxcode.spark.common.utils.http.HttpUtils;
import com.isxcode.spark.modules.cluster.entity.ClusterEntity;
import com.isxcode.spark.modules.cluster.entity.ClusterNodeEntity;
import com.isxcode.spark.modules.cluster.repository.ClusterNodeRepository;
import com.isxcode.spark.modules.cluster.service.ClusterService;
import com.isxcode.spark.modules.datasource.entity.DatasourceEntity;
import com.isxcode.spark.modules.datasource.service.DatasourceService;
import com.isxcode.spark.modules.user.service.UserService;
import com.isxcode.spark.modules.work.service.WorkConfigService;
import com.isxcode.spark.modules.real.entity.RealEntity;
import com.isxcode.spark.modules.real.mapper.RealMapper;
import com.isxcode.spark.modules.real.repository.RealRepository;
import com.isxcode.spark.vip.modules.real.run.RealWorkRun;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.isxcode.spark.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.spark.common.config.CommonConfig.USER_ID;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class RealBizService {

    private final RealRepository realRepository;

    private final ClusterService clusterService;

    private final RealMapper realMapper;

    private final RealService realService;

    private final DatasourceService datasourceService;

    private final RealWorkRun realWorkRun;

    private final ClusterNodeRepository clusterNodeRepository;

    private final IsxAppProperties isxAppProperties;

    private final WorkConfigService workConfigService;
    private final UserService userService;

    public void addReal(AddRealReq addRealReq) {

        clusterService.getCluster(addRealReq.getClusterId());

        RealEntity realEntity = realMapper.addRealReqToRealEntity(addRealReq);

        realEntity.setStatus(RealStatus.NEW);
        realEntity.setSparkConfig(JSON.toJSONString(workConfigService.initSparkConfig(ResourceLevel.LOW)));

        realRepository.save(realEntity);
    }

    public void updateReal(UpdateRealReq updateApiReq) {

        RealEntity real = realService.getReal(updateApiReq.getId());

        real.setName(updateApiReq.getName());
        real.setRemark(updateApiReq.getRemark());

        realRepository.save(real);
    }

    public Page<PageRealRes> pageReal(PageRealReq pageRealReq) {

        Page<RealEntity> realEntityPage = realRepository.searchAll(pageRealReq.getSearchKeyWord(),
            PageRequest.of(pageRealReq.getPage(), pageRealReq.getPageSize()));

        Page<PageRealRes> result = realEntityPage.map(realMapper::realEntityToPageRealRes);

        result.getContent().forEach(e -> {
            e.setClusterName(clusterService.getClusterName(e.getClusterId()));
            e.setCreateUsername(userService.getUserName(e.getCreateBy()));
        });
        return result;
    }

    public void deleteReal(DeleteRealReq deleteApiReq) {

        RealEntity real = realService.getReal(deleteApiReq.getId());

        realRepository.delete(real);
    }

    public void configReal(ConfigRealReq configRealReq) {

        RealEntity real = realService.getReal(configRealReq.getRealId());

        // 运行中，不可编辑配置
        if (RealStatus.RUNNING.equals(real.getStatus()) || RealStatus.DEPLOYING.equals(real.getStatus())) {
            throw new IsxAppException("运行中，不可编辑配置");
        }

        // spark配置
        if (configRealReq.getSparkConfigJson() != null) {
            real.setSparkConfig(configRealReq.getSparkConfigJson());
        }

        // 集群id
        if (!Strings.isEmpty(configRealReq.getClusterId())) {
            real.setClusterId(configRealReq.getClusterId());
        }

        // 设置用户自定义函数
        if (configRealReq.getFuncList() != null) {
            if (!configRealReq.getFuncList().isEmpty()) {
                real.setFuncConfig(JSON.toJSONString(configRealReq.getFuncList()));
            } else {
                real.setFuncConfig("[]");
            }
        }

        // 设置用户程序依赖
        if (configRealReq.getLibList() != null) {
            if (!configRealReq.getLibList().isEmpty()) {
                real.setLibConfig(JSON.toJSONString(configRealReq.getLibList()));
            } else {
                real.setLibConfig("[]");
            }
        }

        // 同步配置
        if (configRealReq.getSyncConfig() != null) {
            real.setSyncConfig(JSON.toJSONString(configRealReq.getSyncConfig()));
        }

        // 数据持久化
        realRepository.save(real);
    }

    public Set<String> queryTopic(QueryTopicReq queryTopicReq) {

        DatasourceEntity datasource = datasourceService.getDatasource(queryTopicReq.getDatasourceId());

        if (!DatasourceStatus.ACTIVE.equals(datasource.getStatus())) {
            throw new IsxAppException("数据源不可用");
        }

        if (!DatasourceType.KAFKA.equals(datasource.getDbType())) {
            throw new IsxAppException("只能处理kafka数据源");
        }

        KafkaConfig kafkaConfig = JSON.parseObject(datasource.getKafkaConfig(), KafkaConfig.class);
        try {
            return datasourceService.queryKafkaTopic(kafkaConfig);
        } catch (ExecutionException | InterruptedException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("数据源不可用");
        }
    }

    public List<String> queryArrayPath(QueryArrayPathReq queryArrayPathReq) {

        Map<String, Object> jsonPath;
        if (JSON.isValidArray(queryArrayPathReq.getJsonStr())) {
            jsonPath = JSONPath.paths(JSON.parseArray(queryArrayPathReq.getJsonStr()));
        } else {
            jsonPath = JSONPath.paths(JSON.parseObject(queryArrayPathReq.getJsonStr()));
        }

        List<String> arrayPath = new ArrayList<>();
        jsonPath.forEach((k, v) -> {
            if (v instanceof JSONArray) {
                arrayPath.add(k);
                arrayPath.add(replaceIndexes(k));
            }
        });

        return arrayPath.stream().distinct().sorted().collect(Collectors.toList());
    }

    public static String replaceIndexes(String input) {

        Pattern pattern = Pattern.compile("\\[(\\d+)]");
        Matcher matcher = pattern.matcher(input);

        StringBuffer output = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(output, "[*]");
        }
        matcher.appendTail(output);
        return output.toString();
    }

    public List<QueryColumnPathRes> queryColumnPath(QueryColumnPathReq queryColumnPathReq) {

        List<QueryColumnPathRes> result = new ArrayList<>();

        if (Strings.isEmpty(queryColumnPathReq.getRootPath())) {
            // 如果是对象，返回所有的path
            Map<String, Object> jsonPaths;
            if (JSON.isValidArray(queryColumnPathReq.getJsonStr())) {
                jsonPaths = JSONPath.paths(JSON.parseArray(queryColumnPathReq.getJsonStr()));
            } else {
                jsonPaths = JSONPath.paths(JSON.parseObject(queryColumnPathReq.getJsonStr()));
            }

            AtomicInteger indexStart = new AtomicInteger();
            jsonPaths.forEach((k, v) -> {
                result.add(QueryColumnPathRes.builder().name("col" + indexStart.get())
                    .type(translateJsonType(v.getClass().getTypeName())).jsonPath(k).build());
                indexStart.getAndIncrement();
            });
            return result;
        } else {
            // 如果是数组，指定rootPath返回
            Map<String, Object> paths = new HashMap<>();
            if (!queryColumnPathReq.getRootPath().contains("*") || "$".equals(queryColumnPathReq.getRootPath())) {

                int dataSize;
                if (JSON.isValidArray(queryColumnPathReq.getJsonStr())) {
                    dataSize = JSON.parseArray(JSON.toJSONString(JSONPath
                        .eval(JSON.parseArray(queryColumnPathReq.getJsonStr()), queryColumnPathReq.getRootPath())))
                        .size();
                } else {
                    dataSize = JSON.parseArray(JSON.toJSONString(JSONPath
                        .eval(JSON.parseObject(queryColumnPathReq.getJsonStr()), queryColumnPathReq.getRootPath())))
                        .size();
                }

                Object eval;
                if (JSON.isValidArray(queryColumnPathReq.getJsonStr())) {
                    for (int i = 0; i < dataSize; i++) {
                        eval = JSONPath.eval(JSON.parseArray(queryColumnPathReq.getJsonStr()),
                            queryColumnPathReq.getRootPath() + "[" + i + "]");
                        paths = JSONPath.paths(eval);
                    }
                } else {
                    for (int i = 0; i < dataSize; i++) {
                        eval = JSONPath.eval(JSON.parseObject(queryColumnPathReq.getJsonStr()),
                            queryColumnPathReq.getRootPath() + "[" + i + "]");
                        paths = JSONPath.paths(eval);
                    }
                }

                paths.forEach((k, v) -> result.add(QueryColumnPathRes.builder().jsonPath(k)
                    .type(translateJsonType(v.getClass().getTypeName())).build()));
            } else {
                Object eval;
                if (JSON.isValidArray(queryColumnPathReq.getJsonStr())) {
                    eval = JSONPath.eval(JSON.parseArray(queryColumnPathReq.getJsonStr()),
                        queryColumnPathReq.getRootPath());
                } else {
                    eval = JSONPath.eval(JSON.parseObject(queryColumnPathReq.getJsonStr()),
                        queryColumnPathReq.getRootPath());
                }

                paths = JSONPath.paths(eval);
                paths.forEach((k, v) -> result.add(QueryColumnPathRes.builder().jsonPath(replaceIndexes(k))
                    .type(translateJsonType(v.getClass().getTypeName())).build()));
            }

            List<QueryColumnPathRes> sortResult = result.stream().distinct()
                .sorted(Comparator.comparing(QueryColumnPathRes::getJsonPath)).collect(Collectors.toList());
            sortResult.forEach(e -> e.setName("col" + sortResult.indexOf(e)));
            return sortResult;
        }
    }

    public String translateJsonType(String classType) {

        switch (classType) {
            case "com.alibaba.fastjson.JSONObject":
                return "object";
            case "com.alibaba.fastjson.JSONArray":
                return "array";
            case "java.lang.Integer":
                return "int";
            case "java.lang.Boolean":
                return "boolean";
            case "java.math.BigDecimal":
                return "bigDecimal";
            default:
                return "string";
        }
    }

    public void startReal(StartRealReq startRealReq) {

        RealEntity realEntity = realService.getReal(startRealReq.getId());

        // 只有停止或者失败,才可以启动
        if (!(RealStatus.STOP.equals(realEntity.getStatus()) || RealStatus.FAIL.equals(realEntity.getStatus())
            || RealStatus.NEW.equals(realEntity.getStatus()))) {
            throw new IsxAppException("作业不可重复启动");
        }

        realEntity.setStatus(RealStatus.DEPLOYING);
        realEntity.setRunningLog("");
        realRepository.save(realEntity);
        String tenantId = TENANT_ID.get();
        String userId = USER_ID.get();

        // 异步发布容器
        CompletableFuture.supplyAsync(() -> {
            try {
                TENANT_ID.set(tenantId);
                USER_ID.set(userId);

                RunRealWorkRes runRealWorkRes = realWorkRun.runRealWork(realEntity);
                runRealWorkRes.setStatus(RealStatus.RUNNING);
                runRealWorkRes.setAppId(runRealWorkRes.getAppId());
                return runRealWorkRes;
            } catch (IsxAppException ex) {
                return RunRealWorkRes.builder().status(RealStatus.FAIL).errLog(ex.getMsg()).build();
            }
        }).whenComplete((result, throwable) -> {
            // 持久化到数据库
            RealEntity realWork = realService.getReal(realEntity.getId());
            if (result.getErrLog() != null) {
                realWork.setSubmitLog(realWork.getSubmitLog() + result.getErrLog());
            }
            realWork.setApplicationId(result.getAppId());
            realWork.setStatus(result.getStatus());
            realRepository.save(realWork);
        });
    }

    public GetRealRes getReal(GetRealReq getRealReq) {

        GetRealRes result = new GetRealRes();

        RealEntity real = realService.getReal(getRealReq.getId());

        if (!Strings.isEmpty(real.getLibConfig())) {
            result.setLibConfig(JSON.parseArray(real.getLibConfig(), String.class));
        }

        if (!Strings.isEmpty(real.getFuncConfig())) {
            result.setFuncConfig(JSON.parseArray(real.getFuncConfig(), String.class));
        }

        if (!Strings.isEmpty(real.getSparkConfig())) {
            result.setSparkConfig(real.getSparkConfig());
        }

        if (!Strings.isEmpty(real.getSyncConfig())) {
            result.setSyncConfig(JSON.parseObject(real.getSyncConfig(), SyncWorkConfig.class));
        }

        result.setClusterId(real.getClusterId());
        result.setId(real.getId());
        return result;
    }

    public void checkReal(CheckRealReq checkRealReq) {

        RealEntity real = realService.getReal(checkRealReq.getId());

        // 部署中,不可以检测
        if (RealStatus.DEPLOYING.equals(real.getStatus())) {
            throw new IsxAppException("发布中的计算不可以检测,请耐心等待");
        }

        // 新建实时作业不能检测
        if (RealStatus.NEW.equals(real.getStatus()) || RealStatus.FAIL.equals(real.getStatus())) {
            throw new IsxAppException("请先运行");
        }

        ClusterEntity cluster = clusterService.getCluster(real.getClusterId());

        // 获取集群节点
        List<ClusterNodeEntity> allEngineNodes =
            clusterNodeRepository.findAllByClusterIdAndStatus(real.getClusterId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException("集群不存在可用节点");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));

        // 获取作业状态并保存
        GetWorkStatusReq getWorkStatusReq = GetWorkStatusReq.builder().appId(real.getApplicationId())
            .clusterType(cluster.getClusterType()).sparkHomePath(engineNode.getSparkHomePath()).build();
        BaseResponse<?> baseResponse;
        try {
            baseResponse = HttpUtils.doPost(
                genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STATUS_URL),
                getWorkStatusReq, BaseResponse.class);
        } catch (HttpServerErrorException e) {
            throw new IsxAppException("检查集群状态:" + e.getMessage());
        }

        if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
            real.setStatus(RealStatus.FAIL);
            real.setSubmitLog(real.getSubmitLog() + LocalDateTime.now() + WorkLog.ERROR_INFO + "状态检测异常:"
                + baseResponse.getMsg() + "\n");
            realRepository.save(real);
            return;
        }

        // 解析返回状态，并保存
        RunWorkRes workStatusRes = JSON.parseObject(JSON.toJSONString(baseResponse.getData()), RunWorkRes.class);
        if (workStatusRes != null && "RUNNING".equalsIgnoreCase(workStatusRes.getAppStatus())) {
            real.setStatus(RealStatus.RUNNING);
            if (real.getSubmitLog().contains("运行状态")) {
                real.setSubmitLog(real.getSubmitLog() + LocalDateTime.now() + WorkLog.SUCCESS_INFO + "运行状态:运行中 \n");
            } else {
                real.setSubmitLog(
                    real.getSubmitLog() + "\n" + LocalDateTime.now() + WorkLog.SUCCESS_INFO + "运行状态:运行中 \n");
            }
        } else {
            real.setStatus(RealStatus.STOP);
            if (real.getSubmitLog().contains("运行状态")) {
                real.setSubmitLog(real.getSubmitLog() + LocalDateTime.now() + WorkLog.SUCCESS_INFO + "运行状态:已停止 \n");
            } else {
                real.setSubmitLog(
                    real.getSubmitLog() + "\n" + LocalDateTime.now() + WorkLog.SUCCESS_INFO + "运行状态:已停止 \n");
            }
        }
        realRepository.save(real);
    }

    public String genHttpUrl(String host, String port, String path) {

        String httpProtocol = isxAppProperties.isUseSsl() ? "https://" : "http://";
        String httpHost = isxAppProperties.isUsePort() ? host + ":" + port : host;

        return httpProtocol + httpHost + path;
    }

    public void stopReal(StopRealReq stopRealReq) {

        RealEntity real = realService.getReal(stopRealReq.getId());
        ClusterEntity cluster = clusterService.getCluster(real.getClusterId());

        // 新建实时作业不能检测
        if (RealStatus.NEW.equals(real.getStatus())) {
            throw new IsxAppException("请先运行");
        }

        // 新建实时作业不能检测
        if (RealStatus.FAIL.equals(real.getStatus()) || RealStatus.STOP.equals(real.getStatus())) {
            throw new IsxAppException("已停止");
        }

        // 获取集群节点
        List<ClusterNodeEntity> allEngineNodes =
            clusterNodeRepository.findAllByClusterIdAndStatus(real.getClusterId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException("集群不存在可用节点");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));

        StopWorkReq stopWorkReq =
            StopWorkReq.builder().appId(real.getApplicationId()).clusterType(cluster.getClusterType())
                .sparkHomePath(engineNode.getSparkHomePath()).agentHomePath(engineNode.getAgentHomePath()).build();
        HttpUtils.doPost(genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.STOP_WORK_URL),
            stopWorkReq, BaseResponse.class);

        // 修改real状态
        real.setStatus(RealStatus.STOP);
        real.setSubmitLog(real.getSubmitLog() + LocalDateTime.now() + WorkLog.SUCCESS_INFO + "运行状态:已手动停止");
        realRepository.save(real);
    }

    public GetRealSubmitLogRes getRealSubmitLog(GetRealSubmitLogReq getRealSubmitLogReq) {

        RealEntity real = realService.getReal(getRealSubmitLogReq.getId());

        return realMapper.realEntityToGetRealSubmitLogRes(real);
    }

    public GetRealRunningLogRes getRealRunningLog(GetRealRunningLogReq getRealRunningLogReq) {

        GetRealRunningLogRes result = new GetRealRunningLogRes();
        result.setId(getRealRunningLogReq.getId());

        RealEntity real = realService.getReal(getRealRunningLogReq.getId());
        ClusterEntity cluster = clusterService.getCluster(real.getClusterId());

        if (RealStatus.NEW.equals(real.getStatus()) || RealStatus.DEPLOYING.equals(real.getStatus())) {
            throw new IsxAppException("运行中的日志才可以查询,请耐心等待");
        }

        if (RealStatus.FAIL.equals(real.getStatus()) || RealStatus.STOP.equals(real.getStatus())) {
            return GetRealRunningLogRes.builder().runningLog(real.getRunningLog()).status(real.getStatus()).build();
        }

        // 获取集群节点
        List<ClusterNodeEntity> allEngineNodes =
            clusterNodeRepository.findAllByClusterIdAndStatus(real.getClusterId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException("集群不存在可用节点");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));

        // 先查看任务的运行状态,如果是失败的则直接打印错误日志
        GetWorkStatusReq getWorkStatusReq = GetWorkStatusReq.builder().appId(real.getApplicationId())
            .clusterType(cluster.getClusterType()).sparkHomePath(engineNode.getSparkHomePath()).build();
        BaseResponse<?> baseResponseStatus =
            HttpUtils.doPost(genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STATUS_URL),
                getWorkStatusReq, BaseResponse.class);
        if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponseStatus.getCode())) {
            throw new IsxAppException(
                LocalDateTime.now() + WorkLog.ERROR_INFO + "获取作业状态异常 : " + baseResponseStatus.getMsg() + "\n");
        }
        RunWorkRes workStatusRes = com.alibaba.fastjson2.JSON
            .parseObject(com.alibaba.fastjson2.JSON.toJSONString(baseResponseStatus.getData()), RunWorkRes.class);
        if ("FAILED".equals(workStatusRes.getAppStatus()) || "Error".equals(workStatusRes.getAppStatus())) {

            GetWorkStderrLogReq getWorkStderrLogReq = GetWorkStderrLogReq.builder().appId(real.getApplicationId())
                .clusterType(cluster.getClusterType()).sparkHomePath(engineNode.getSparkHomePath()).build();
            BaseResponse<?> baseResponseError = HttpUtils.doPost(
                genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STDERR_LOG_URL),
                getWorkStderrLogReq, BaseResponse.class);
            if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponseError.getCode())) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "获取作业日志异常 : " + baseResponseError.getMsg() + "\n");
            }

            // 解析日志并保存
            GetWorkStderrLogRes yagGetLogRes =
                JSON.parseObject(JSON.toJSONString(baseResponseError.getData()), GetWorkStderrLogRes.class);
            real.setRunningLog(real.getRunningLog() + yagGetLogRes.getLog());
            real.setStatus(RealStatus.STOP);
            real.setSubmitLog(real.getSubmitLog() + "\n" + LocalDateTime.now() + WorkLog.ERROR_INFO + "运行状态:异常中断");
            realRepository.save(real);
            return GetRealRunningLogRes.builder().runningLog(real.getRunningLog()).status(real.getStatus()).build();
        }

        // 如果是失败的则,打印错误日志
        GetWorkStdoutLogReq getWorkStdoutLogReq = GetWorkStdoutLogReq.builder().appId(real.getApplicationId())
            .clusterType(cluster.getClusterType()).sparkHomePath(engineNode.getSparkHomePath()).build();
        BaseResponse<?> baseResponse = HttpUtils.doPost(
            genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STDOUT_LOG_URL),
            getWorkStdoutLogReq, BaseResponse.class);

        if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
            result.setRunningLog(real.getRunningLog() + LocalDateTime.now() + WorkLog.ERROR_INFO + "获取作业日志异常 : "
                + baseResponse.getMsg() + "\n");
        }

        // 解析日志并保存
        GetWorkStdoutLogRes yagGetStdoutLogRes =
            JSON.parseObject(JSON.toJSONString(baseResponse.getData()), GetWorkStdoutLogRes.class);
        if (yagGetStdoutLogRes != null) {

            // 数据比之前大的就添加日志
            if (!Strings.isEmpty(real.getRunningLog())) {
                List<String> logList = Arrays.asList(real.getRunningLog().split("\n"));
                if (!logList.isEmpty() && !"\n".equals(yagGetStdoutLogRes.getLog())) {
                    long preNum = Long.parseLong(logList.get(logList.size() - 1).split(":")[4].trim());
                    long nowNum = Long.parseLong(yagGetStdoutLogRes.getLog().split(":")[4].trim());
                    if (nowNum > preNum) {
                        result.setRunningLog(real.getRunningLog() + yagGetStdoutLogRes.getLog());
                    } else {
                        result.setRunningLog(real.getRunningLog());
                    }
                } else {
                    result.setRunningLog(yagGetStdoutLogRes.getLog());
                }
            } else {
                result.setRunningLog(yagGetStdoutLogRes.getLog());
            }
        }

        // 持久化日志
        real.setRunningLog(result.getRunningLog());
        result.setStatus(real.getStatus());
        realRepository.save(real);

        return result;
    }
}
