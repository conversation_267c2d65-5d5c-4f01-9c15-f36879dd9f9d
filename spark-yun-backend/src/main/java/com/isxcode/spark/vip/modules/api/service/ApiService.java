package com.isxcode.spark.vip.modules.api.service;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.api.entity.ApiEntity;
import com.isxcode.spark.modules.api.repository.ApiRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ApiService {

    private final ApiRepository apiRepository;

    public ApiEntity getApi(String apiId) {

        return apiRepository.findById(apiId).orElseThrow(() -> new IsxAppException("Api不存在"));
    }
}
