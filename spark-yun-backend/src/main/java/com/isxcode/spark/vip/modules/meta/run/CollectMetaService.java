package com.isxcode.spark.vip.modules.meta.run;

import com.isxcode.spark.api.datasource.dto.ConnectInfo;
import com.isxcode.spark.api.datasource.dto.QueryColumnDto;
import com.isxcode.spark.api.datasource.dto.QueryTableDto;
import com.isxcode.spark.api.meta.constant.CollectType;
import com.isxcode.spark.api.meta.constant.MetaInstanceStatus;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.datasource.entity.DatasourceEntity;
import com.isxcode.spark.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.spark.modules.datasource.service.DatasourceService;
import com.isxcode.spark.modules.datasource.source.DataSourceFactory;
import com.isxcode.spark.modules.datasource.source.Datasource;
import com.isxcode.spark.modules.meta.entity.MetaColumnEntity;
import com.isxcode.spark.modules.meta.entity.MetaInstanceEntity;
import com.isxcode.spark.modules.meta.entity.MetaTableEntity;
import com.isxcode.spark.modules.meta.entity.MetaWorkEntity;
import com.isxcode.spark.modules.meta.mapper.MetaMapper;
import com.isxcode.spark.modules.meta.repository.MetaColumnRepository;
import com.isxcode.spark.modules.meta.repository.MetaInstanceRepository;
import com.isxcode.spark.modules.meta.repository.MetaTableRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.isxcode.spark.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.spark.common.config.CommonConfig.USER_ID;

@Service
@RequiredArgsConstructor
public class CollectMetaService {

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceService datasourceService;

    private final MetaTableRepository metaTableRepository;

    private final MetaColumnRepository metaColumnRepository;

    private final MetaInstanceRepository metaInstanceRepository;

    private final MetaMapper metaMapper;

    private final DatasourceMapper datasourceMapper;

    public static final Map<String, Thread> META_WORK_THREAD = new HashMap<>();

    public void collect(MetaWorkEntity metaWork, String metaWorkInstanceId) {

        META_WORK_THREAD.put(metaWorkInstanceId, Thread.currentThread());

        Optional<MetaInstanceEntity> metaInstanceEntity = metaInstanceRepository.findById(metaWorkInstanceId);
        MetaInstanceEntity metaInstance = metaInstanceEntity.get();

        try {
            // 采集数据
            collectData(metaWork);
            metaInstance.setStatus(MetaInstanceStatus.SUCCESS);
            metaInstance.setEndDateTime(LocalDateTime.now());
            metaInstance.setCollectLog("采集成功");

            // 采集成功
            META_WORK_THREAD.remove(metaInstance.getId());
            metaInstanceRepository.save(metaInstance);
        } catch (IsxAppException e) {

            // 采集失败
            metaInstance.setStatus(MetaInstanceStatus.FAIL);
            metaInstance.setEndDateTime(LocalDateTime.now());
            metaInstance.setCollectLog(e.getMsg());
            META_WORK_THREAD.remove(metaInstance.getId());
            metaInstanceRepository.save(metaInstance);
        }
    }

    /**
     * 采集元数据.
     */
    public void collectData(MetaWorkEntity metaWork) {

        // 获取数据源信息
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(metaWork.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 获取表名正则表达式
        String tablePattern;
        if (CollectType.CUSTOM_TABLE.equals(metaWork.getCollectType())
            && Strings.isNotEmpty(metaWork.getTablePattern())) {
            tablePattern = metaWork.getTablePattern();
        } else {
            tablePattern = "";
        }

        // 封装采集对象
        connectInfo.setDatasourceId(datasourceEntity.getId());
        connectInfo.setDatabase(datasource.parseDbName(datasourceEntity.getJdbcUrl()));
        connectInfo.setTablePattern(tablePattern);
        List<QueryTableDto> queryTableDtos = datasource.queryTable(connectInfo);

        // 先删除所有相关的table
        if (CollectType.ALL_TABLE.equals(metaWork.getCollectType())) {
            metaTableRepository.deleteAllByDatasourceId(datasourceEntity.getId());
        } else {
            List<String> tableNames =
                queryTableDtos.stream().map(QueryTableDto::getTableName).collect(Collectors.toList());
            metaTableRepository.deleteAllByDatasourceIdAndTableNameIn(datasourceEntity.getId(), tableNames);
        }

        // 一次性插入表的元数据
        List<MetaTableEntity> metaTableEntities = metaMapper.queryTableDtoListToMetaTableEntityList(queryTableDtos);
        metaTableRepository.saveAll(metaTableEntities);

        // 采集表的字段
        queryTableDtos.forEach(queryTableDto -> {

            // 表名不符合则跳过
            if (CollectType.CUSTOM_TABLE.equals(metaWork.getCollectType())
                && Strings.isNotEmpty(metaWork.getTablePattern())) {
                if (!Pattern.matches(metaWork.getTablePattern(), queryTableDto.getTableName())) {
                    return;
                }
            }

            // 获取表的字段信息
            connectInfo.setTableName(queryTableDto.getTableName());
            List<QueryColumnDto> queryColumnDtos = datasource.queryColumn(connectInfo);
            metaColumnRepository.deleteAllByDatasourceIdAndTableName(datasourceEntity.getId(),
                queryTableDto.getTableName());
            List<MetaColumnEntity> metaColumnEntities =
                metaMapper.queryColumnDtoListToMetaColumnEntityList(queryColumnDtos);
            metaColumnRepository.saveAll(metaColumnEntities);
        });
    }

    @Transactional
    public void syncCollect(MetaWorkEntity metaWork, String metaWorkInstanceId) {
        collect(metaWork, metaWorkInstanceId);
    }

    @Async
    @Transactional
    public void asyncCollect(MetaWorkEntity metaWork, String metaWorkInstanceId, String userId, String tenantId) {

        USER_ID.set(userId);
        TENANT_ID.set(tenantId);
        collect(metaWork, metaWorkInstanceId);
    }
}
