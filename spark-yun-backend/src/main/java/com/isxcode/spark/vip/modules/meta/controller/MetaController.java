package com.isxcode.spark.vip.modules.meta.controller;

import com.isxcode.spark.api.datasource.dto.QueryColumnDto;
import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.meta.req.*;
import com.isxcode.spark.api.meta.res.*;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.api.work.res.GetDataSourceDataRes;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.meta.service.MetaBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "元数据模块")
@RequestMapping(ModuleVipCode.VIP_META)
@RestController
@RequiredArgsConstructor
public class MetaController {

    private final MetaBizService metaBizService;

    @VipApi
    @Operation(summary = "查询元数据数据源接口")
    @PostMapping("/pageMetaDatabase")
    @SuccessResponse("查询成功")
    public Page<PageMetaDatabaseRes> pageMetaDatabase(@Valid @RequestBody PageMetaDatabaseReq pageMetaDatabaseReq) {

        return metaBizService.pageMetaDatabase(pageMetaDatabaseReq);
    }

    @VipApi
    @Operation(summary = "刷新元数据数据源接口")
    @PostMapping("/refreshMetaDatabase")
    @SuccessResponse("刷新成功")
    public void refreshMetaDatabase() {

        metaBizService.refreshMetaDatabase();
    }

    @VipApi
    @Operation(summary = "查询元数据表接口")
    @PostMapping("/pageMetaTable")
    @SuccessResponse("查询成功")
    public Page<PageMetaTableRes> pageMetaTable(@Valid @RequestBody PageMetaTableReq pageMetaTableReq) {

        return metaBizService.pageMetaTable(pageMetaTableReq);
    }

    @VipApi
    @Operation(summary = "创建元数据采集任务接口")
    @PostMapping("/addMetaWork")
    @SuccessResponse("添加成功")
    public AddMetaWokRes addMetaWork(@Valid @RequestBody AddMetaWokReq addMetaWokReq) {

        return metaBizService.addMetaWork(addMetaWokReq);
    }

    @VipApi
    @Operation(summary = "更新采集任务接口")
    @PostMapping("/updateMetaWork")
    @SuccessResponse("更新成功")
    public void updateMetaWork(@Valid @RequestBody UpdateMetaWokReq updateMetaWokReq) {

        metaBizService.updateMetaWork(updateMetaWokReq);
    }

    @VipApi
    @Operation(summary = "分页查询采集任务接口")
    @PostMapping("/pageMetaWork")
    @SuccessResponse("查询成功")
    public Page<PageMetaWorkRes> pageMetaWork(@Valid @RequestBody PageMetaWorkReq pageMetaWorkReq) {

        return metaBizService.pageMetaWork(pageMetaWorkReq);
    }

    @VipApi
    @Operation(summary = "执行采集接口")
    @PostMapping("/triggerMetaWork")
    @SuccessResponse("提交成功")
    public void triggerMetaWork(@Valid @RequestBody TriggerMetaWokReq triggerMetaWokReq) {

        metaBizService.triggerMetaWork(triggerMetaWokReq);
    }

    @VipApi
    @Operation(summary = "立即执行采集接口")
    @PostMapping("/fastTriggerMetaWork")
    @SuccessResponse("采集成功")
    public void fastTriggerMetaWork(@Valid @RequestBody TriggerMetaWokReq triggerMetaWokReq) {

        metaBizService.fastTriggerMetaWork(triggerMetaWokReq);
    }

    @VipApi
    @Operation(summary = "分页查询字段接口")
    @PostMapping("/pageMetaColumn")
    @SuccessResponse("查询成功")
    public Page<PageMetaColumnRes> pageMetaColumn(@Valid @RequestBody PageMetaColumnReq pageMetaColumnReq) {

        return metaBizService.pageMetaColumn(pageMetaColumnReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_ADMIN})
    @Operation(summary = "删除元数据采集任务接口")
    @PostMapping("/deleteMetaWork")
    @SuccessResponse("删除成功")
    public void deleteMetaWork(@Valid @RequestBody DeleteMetaWokReq deleteMetaWokReq) {

        metaBizService.deleteMetaWork(deleteMetaWokReq);
    }

    @VipApi
    @Operation(summary = "分页查询采集实例接口")
    @PostMapping("/pageMetaWorkInstance")
    @SuccessResponse("查询成功")
    public Page<PageMetaWorkInstanceRes> pageMetaWorkInstance(
        @Valid @RequestBody PageMetaWorkInstanceReq pageMetaWorkInstanceReq) {

        return metaBizService.pageMetaWorkInstance(pageMetaWorkInstanceReq);
    }

    @VipApi
    @Operation(summary = "删除采集实例接口")
    @PostMapping("/deleteMetaWorkInstance")
    @SuccessResponse("删除成功")
    public void deleteMetaWorkInstance(@Valid @RequestBody DeleteMetaWokInstanceReq deleteMetaWokInstanceReq) {

        metaBizService.deleteMetaWorkInstance(deleteMetaWokInstanceReq);
    }

    @VipApi
    @Operation(summary = "中止采集实例接口")
    @PostMapping("/abortMetaWorkInstance")
    @SuccessResponse("中止成功")
    public void abortMetaWorkInstance(@Valid @RequestBody AbortMetaWokInstanceReq abortMetaWokInstanceReq) {

        metaBizService.abortMetaWorkInstance(abortMetaWokInstanceReq);
    }

    @VipApi
    @Operation(summary = "开启采集任务接口")
    @PostMapping("/enableMetaWork")
    @SuccessResponse("启用成功")
    public void enableMetaWork(@Valid @RequestBody EnableMetaWokReq enableMetaWokReq) {

        metaBizService.enableMetaWork(enableMetaWokReq);
    }

    @VipApi
    @Operation(summary = "关闭采集任务接口")
    @PostMapping("/disableMetaWork")
    @SuccessResponse("关闭成功")
    public void disableMetaWork(@Valid @RequestBody DisableMetaWokReq disableMetaWokReq) {

        metaBizService.disableMetaWork(disableMetaWokReq);
    }

    @VipApi
    @Operation(summary = "刷新表基础信息接口")
    @PostMapping("/refreshMetaTableInfo")
    @SuccessResponse("刷新成功")
    public GetMetaTableInfoRes refreshMetaTableInfo(
        @Valid @RequestBody RefreshMetaTableInfoReq refreshMetaTableInfoReq) {

        return metaBizService.refreshMetaTableInfo(refreshMetaTableInfoReq);
    }

    @VipApi
    @Operation(summary = "查询表基础信息接口")
    @PostMapping("/getMetaTableInfo")
    @SuccessResponse("查询成功")
    public GetMetaTableInfoRes getMetaTableInfo(@Valid @RequestBody GetMetaTableInfoReq getMetaTableInfoReq) {

        return metaBizService.getMetaTableInfo(getMetaTableInfoReq);
    }

    @VipApi
    @Operation(summary = "查看表字段列表接口")
    @PostMapping("/getMetaTableColumn")
    @SuccessResponse("查询成功")
    public List<QueryColumnDto> getMetaTableColumn(@Valid @RequestBody GetMetaTableColumnReq getMetaTableColumnReq) {

        return metaBizService.getMetaTableColumn(getMetaTableColumnReq);
    }

    @VipApi
    @Operation(summary = "查看表预览数据接口")
    @PostMapping("/getMetaTableData")
    @SuccessResponse("查询成功")
    public GetDataSourceDataRes getMetaTableData(@Valid @RequestBody GetMetaTableDataReq getMetaTableDataReq) {

        return metaBizService.getMetaTableData(getMetaTableDataReq);
    }

    @VipApi
    @Operation(summary = "导出表excel接口")
    @PostMapping("/exportTableExcel")
    public void exportTableExcel(@Valid @RequestBody ExportTableExcelReq exportTableExcelReq,
        HttpServletResponse response) {

        metaBizService.exportTableExcel(exportTableExcelReq, response);
    }

    @VipApi
    @Operation(summary = "修改元数据db的备注")
    @PostMapping("/updateDatabaseComment")
    @SuccessResponse("备注成功")
    public void updateDatabaseComment(@Valid @RequestBody UpdateDatabaseCommentReq updateDatabaseCommentReq) {

        metaBizService.updateDatabaseComment(updateDatabaseCommentReq);
    }

    @VipApi
    @Operation(summary = "修改元数据表的备注")
    @PostMapping("/updateTableComment")
    @SuccessResponse("备注成功")
    public void updateTableComment(@Valid @RequestBody UpdateTableCommentReq updateTableCommentReq) {

        metaBizService.updateTableComment(updateTableCommentReq);
    }

    @VipApi
    @Operation(summary = "修改元数据字段的备注")
    @PostMapping("/updateColumnComment")
    @SuccessResponse("备注成功")
    public void updateColumnComment(@Valid @RequestBody UpdateColumnCommentReq updateColumnCommentReq) {

        metaBizService.updateColumnComment(updateColumnCommentReq);
    }
}
