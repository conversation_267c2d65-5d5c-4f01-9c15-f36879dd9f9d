package com.isxcode.spark.vip.modules.view.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.isxcode.spark.api.datasource.dto.ConnectInfo;
import com.isxcode.spark.api.view.constants.ViewCardType;
import com.isxcode.spark.api.view.dto.DataSql;
import com.isxcode.spark.api.view.dto.EchartOption;
import com.isxcode.spark.api.view.dto.EchartSeries;
import com.isxcode.spark.api.view.dto.PieData;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.datasource.entity.DatasourceEntity;
import com.isxcode.spark.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.spark.modules.datasource.service.DatasourceService;
import com.isxcode.spark.modules.datasource.source.DataSourceFactory;
import com.isxcode.spark.modules.datasource.source.Datasource;
import com.isxcode.spark.modules.view.entity.ViewCardEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class BasicPieViewDataExecutor extends ViewDataExecutor {

    private final DatasourceService datasourceService;

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceMapper datasourceMapper;

    public BasicPieViewDataExecutor(DatasourceService datasourceService, DataSourceFactory dataSourceFactory,
        DatasourceMapper datasourceMapper) {
        super(datasourceService);
        this.datasourceService = datasourceService;
        this.dataSourceFactory = dataSourceFactory;
        this.datasourceMapper = datasourceMapper;
    }

    @Override
    protected EchartOption getData(ViewCardEntity viewCardEntity, DataSql dataSql) {

        List<PieData> pieData = new ArrayList<>();
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(viewCardEntity.getDatasourceId());

        for (String sql : dataSql.getSqlList()) {

            ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
            Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());
            connectInfo.setLoginTimeout(5);
            try (Connection connection = datasource.getConnection(connectInfo);
                Statement statement = connection.createStatement()) {

                statement.setQueryTimeout(1800);
                ResultSet resultSet = statement.executeQuery(sql);

                // 封装数据
                while (resultSet.next()) {
                    pieData.add(PieData.builder().name(resultSet.getString(1)).value(resultSet.getDouble(2)).build());
                }
            } catch (IsxAppException e) {
                throw e;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new IsxAppException(e.getMessage());
            }
        }

        EchartOption echartOption = JSON.parseObject(viewCardEntity.getExampleData(), EchartOption.class);
        if (echartOption.getSeries() != null && echartOption.getSeries().get(0) != null) {
            echartOption.getSeries().get(0).setData(JSONArray.parseArray(JSON.toJSONString(pieData), Object.class));
        } else {
            echartOption.setSeries(Collections.singletonList(
                EchartSeries.builder().data(JSONArray.parseArray(JSON.toJSONString(pieData), Object.class)).build()));
        }
        return echartOption;
    }

    @Override
    public String getViewType() {
        return ViewCardType.PIE;
    }
}
