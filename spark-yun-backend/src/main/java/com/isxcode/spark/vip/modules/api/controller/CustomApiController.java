package com.isxcode.spark.vip.modules.api.controller;

import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.vip.modules.api.service.ApiBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "自定义接口模块")
@Slf4j
@RestController
@RequiredArgsConstructor
public class CustomApiController {

    private final ApiBizService apiBizService;

    @Operation(summary = "自定义POST接口")
    @PostMapping(value = {"/{tenantId}/api/{path1}", "/{tenantId}/api/{path1}/{path2}",
            "/{tenantId}/api/{path1}/{path2}/{path3}"})
    public ResponseEntity<Object> postApi(HttpServletRequest httpServletRequest, @PathVariable String tenantId,
        @PathVariable String path1, @PathVariable(required = false) String path2,
        @PathVariable(required = false) String path3, @RequestBody(required = false) Object requestBody) {

        List<String> paths = new ArrayList<>();
        paths.add(path1);
        paths.add(path2);
        paths.add(path3);
        paths = paths.stream().filter(Objects::nonNull).collect(Collectors.toList());
        String path = StringUtils.join(paths, "/");

        try {
            return apiBizService.postApi(httpServletRequest, tenantId, path, requestBody);
        } catch (IsxAppException e) {
            log.debug(e.getMessage(), e);
            if (e.getCode() == null) {
                return new ResponseEntity<>(e.getMsg(), HttpStatus.INTERNAL_SERVER_ERROR);
            }
            switch (e.getCode()) {
                case "404":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.NOT_FOUND);
                case "400":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.BAD_REQUEST);
                case "401":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.UNAUTHORIZED);
                case "403":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.FORBIDDEN);
                default:
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
    }

    @Operation(summary = "自定义GET接口")
    @GetMapping(value = {"/{tenantId}/api/{path1}", "/{tenantId}/api/{path1}/{path2}",
            "/{tenantId}/api/{path1}/{path2}/{path3}"})
    public ResponseEntity<Object> getApi(HttpServletRequest httpServletRequest, @PathVariable String tenantId,
        @PathVariable String path1, @PathVariable(required = false) String path2,
        @PathVariable(required = false) String path3, @RequestParam(required = false) Map<String, String> requestBody) {

        List<String> paths = new ArrayList<>();
        paths.add(path1);
        paths.add(path2);
        paths.add(path3);
        paths = paths.stream().filter(Objects::nonNull).collect(Collectors.toList());
        String path = StringUtils.join(paths, "/");

        try {
            return apiBizService.getApi(httpServletRequest, tenantId, path, requestBody);
        } catch (IsxAppException e) {
            log.error(e.getMessage());
            if (e.getCode() == null) {
                return new ResponseEntity<>(e.getMsg(), HttpStatus.INTERNAL_SERVER_ERROR);
            }
            switch (e.getCode()) {
                case "404":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.NOT_FOUND);
                case "400":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.BAD_REQUEST);
                case "401":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.UNAUTHORIZED);
                case "403":
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.FORBIDDEN);
                default:
                    return new ResponseEntity<>(e.getMsg(), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
    }

}
