package com.isxcode.spark.vip.modules.real.controller;

import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.real.req.*;
import com.isxcode.spark.api.real.res.*;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.real.service.RealBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

@Tag(name = "实时作业模块")
@RequestMapping(ModuleVipCode.VIP_REAL)
@RestController
@RequiredArgsConstructor
public class RealController {

    private final RealBizService realBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "创建实时作业接口")
    @PostMapping("/addReal")
    @SuccessResponse("添加成功")
    public void addReal(@Valid @RequestBody AddRealReq addApiReq) {

        realBizService.addReal(addApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "更新实时作业接口")
    @PostMapping("/updateReal")
    @SuccessResponse("更新成功")
    public void updateReal(@Valid @RequestBody UpdateRealReq updateApiReq) {

        realBizService.updateReal(updateApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页实时作业列表接口")
    @PostMapping("/pageReal")
    @SuccessResponse("查询成功")
    public Page<PageRealRes> pageReal(@Valid @RequestBody PageRealReq pageRealReq) {

        return realBizService.pageReal(pageRealReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_ADMIN})
    @Operation(summary = "删除实时作业接口")
    @PostMapping("/deleteReal")
    @SuccessResponse("删除成功")
    public void deleteReal(@Valid @RequestBody DeleteRealReq deleteApiReq) {

        realBizService.deleteReal(deleteApiReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "配置实时作业接口")
    @PostMapping("/configReal")
    @SuccessResponse("配置成功")
    public void configReal(@Valid @RequestBody ConfigRealReq configRealReq) {

        realBizService.configReal(configRealReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询topic列表接口")
    @PostMapping("/queryTopic")
    @SuccessResponse("查询成功")
    public Set<String> queryTopic(@Valid @RequestBody QueryTopicReq queryTopicReq) {

        return realBizService.queryTopic(queryTopicReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询json的数组节点接口")
    @PostMapping("/queryArrayPath")
    @SuccessResponse("查询成功")
    public List<String> queryArrayPath(@Valid @RequestBody QueryArrayPathReq queryArrayPathReq) {

        return realBizService.queryArrayPath(queryArrayPathReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询json字段节点接口")
    @PostMapping("/queryColumnPath")
    @SuccessResponse("查询成功")
    public List<QueryColumnPathRes> queryColumnPath(@Valid @RequestBody QueryColumnPathReq queryColumnPathReq) {

        return realBizService.queryColumnPath(queryColumnPathReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "运行实时作业接口")
    @PostMapping("/startReal")
    @SuccessResponse("部署中")
    public void startReal(@Valid @RequestBody StartRealReq startRealReq) {

        realBizService.startReal(startRealReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "获取实时作业详情接口")
    @PostMapping("/getReal")
    @SuccessResponse("查询成功")
    public GetRealRes getReal(@Valid @RequestBody GetRealReq getRealReq) {

        return realBizService.getReal(getRealReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "检测实时作业接口")
    @PostMapping("/checkReal")
    @SuccessResponse("检测成功")
    public void checkReal(@Valid @RequestBody CheckRealReq checkRealReq) {

        realBizService.checkReal(checkRealReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "停止实时作业接口")
    @PostMapping("/stopReal")
    @SuccessResponse("停止成功")
    public void stopReal(@Valid @RequestBody StopRealReq stopRealReq) {

        realBizService.stopReal(stopRealReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "获取实时作业提交日志接口")
    @PostMapping("/getRealSubmitLog")
    @SuccessResponse("获取成功")
    public GetRealSubmitLogRes getRealSubmitLog(@Valid @RequestBody GetRealSubmitLogReq getRealSubmitLogReq) {

        return realBizService.getRealSubmitLog(getRealSubmitLogReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "获取实时作业运行日志接口")
    @PostMapping("/getRealRunningLog")
    @SuccessResponse("获取成功")
    public GetRealRunningLogRes getRealRunningLog(@Valid @RequestBody GetRealRunningLogReq getRealRunningLogReq) {

        return realBizService.getRealRunningLog(getRealRunningLogReq);
    }

}
