package com.isxcode.spark.vip.modules.auth.sso.impl;

import com.alibaba.fastjson.JSONPath;
import com.isxcode.spark.api.auth.constants.OssType;
import com.isxcode.spark.api.auth.dto.SsoAccessTokenParams;
import com.isxcode.spark.common.utils.http.HttpUtils;
import com.isxcode.spark.modules.auth.entity.AuthEntity;
import com.isxcode.spark.vip.modules.auth.sso.SsoAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class GithubSso extends SsoAuth {

    @Override
    public String getSsoType() {
        return OssType.GITHUB;
    }

    @Override
    public String getAccount(AuthEntity authEntity, String code) {

        // 通过code获取token
        SsoAccessTokenParams ssoAccessTokenParams = SsoAccessTokenParams.builder().clientId(authEntity.getClientId())
            .clientSecret(authEntity.getClientSecret()).code(code).redirectUri(authEntity.getRedirectUrl()).build();
        Map<String, String> accessTokenMap =
            HttpUtils.doPost(authEntity.getAccessTokenUrl(), ssoAccessTokenParams, Map.class);

        // 通过token获取用户信息
        Map<String, String> getUserParams = new HashMap<>();
        getUserParams.put("Authorization", "Bearer " + accessTokenMap.get("access_token"));
        String userInfoStr = HttpUtils.doGet(authEntity.getUserUrl(), getUserParams, String.class);
        log.debug("单点获取用户信息:{}", userInfoStr);

        // 通过jsonPath解析用户的账号
        return String.valueOf(JSONPath.read(userInfoStr, authEntity.getAuthJsonPath()));
    }

    @Override
    public String getInvokeUrl(AuthEntity authEntity) {
        return authEntity.getAuthUrl() + "?client_id=" + authEntity.getClientId() + "&redirect_uri="
            + authEntity.getRedirectUrl();
    }
}
