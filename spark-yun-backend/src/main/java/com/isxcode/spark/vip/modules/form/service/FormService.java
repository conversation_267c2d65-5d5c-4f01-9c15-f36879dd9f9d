package com.isxcode.spark.vip.modules.form.service;

import com.alibaba.fastjson.JSON;
import com.isxcode.spark.api.api.dto.FormOptionValue;
import com.isxcode.spark.api.datasource.constants.ColumnType;
import com.isxcode.spark.api.datasource.dto.SecurityColumnDto;
import com.isxcode.spark.api.form.constants.FormComponentType;
import com.isxcode.spark.api.form.dto.FormComponentDto;
import com.isxcode.spark.backend.api.base.exceptions.IsxAppException;
import com.isxcode.spark.modules.form.entity.FormComponentEntity;
import com.isxcode.spark.modules.form.entity.FormEntity;
import com.isxcode.spark.modules.form.repository.FormComponentRepository;
import com.isxcode.spark.modules.form.repository.FormRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class FormService {

    private final FormRepository formRepository;

    private final FormComponentRepository formComponentRepository;

    public FormEntity getForm(String formId) {

        return formRepository.findById(formId).orElseThrow(() -> new IsxAppException("表单不存在"));
    }

    public FormComponentEntity getFormComponent(String formComponentId) {

        return formComponentRepository.findById(formComponentId).orElseThrow(() -> new IsxAppException("表单字段不存在"));
    }

    public Map<String, FormComponentDto> getComponentMap(String formId) {

        Map<String, FormComponentDto> result = new HashMap<>();

        List<FormComponentEntity> allComponents = formComponentRepository.findAllByFormId(formId);
        allComponents.forEach(e -> {
            FormComponentDto formComponentDto = JSON.parseObject(e.getComponentConfig(), FormComponentDto.class);
            if (!FormComponentType.FORM_STATIC_EMPTY.equals(formComponentDto.getComponentType())
                && !FormComponentType.FORM_STATIC_PLACEHOLDER.equals(formComponentDto.getComponentType())) {
                result.put(e.getUuid(), formComponentDto);
            }
        });

        return result;
    }

    public String transComponentType(String componentType) {

        switch (componentType) {
            case FormComponentType.FORM_INPUT_SELECT:
            case FormComponentType.FORM_INPUT_RADIO:
            case FormComponentType.FORM_INPUT_CHECKBOX:
            case FormComponentType.FORM_INPUT_PHONE:
            case FormComponentType.FORM_INPUT_EMAIL:
            case FormComponentType.FORM_INPUT_TEXT:
                return ColumnType.STRING;
            case FormComponentType.FORM_INPUT_NUMBER:
                return ColumnType.DOUBLE;
            case FormComponentType.FORM_INPUT_MONEY:
                return ColumnType.BIG_DECIMAL;
            case FormComponentType.FORM_INPUT_DATE:
                return ColumnType.TIMESTAMP;
            case FormComponentType.FORM_INPUT_SWITCH:
                return ColumnType.BOOLEAN;
        }
        throw new IsxAppException("不支持该组件类型");
    }

    public Object transComponentValue(FormComponentDto component, Object componentValue) {

        if (componentValue == null) {
            return null;
        }

        switch (component.getComponentType()) {
            // 时间类型
            case FormComponentType.FORM_INPUT_DATE:
                String dateStr = String.valueOf(componentValue);
                if ("month".equals(component.getDateType())) {
                    dateStr = dateStr + "-01";
                } else if ("year".equals(component.getDateType())) {
                    dateStr = dateStr + "-01-01";
                }
                LocalDate date = LocalDate.parse(dateStr);
                LocalDateTime dateTime = date.atStartOfDay();
                return dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            default:
                return componentValue;
        }
    }

    public String getColumnType(String componentType) {

        switch (componentType) {
            case FormComponentType.FORM_INPUT_SELECT:
            case FormComponentType.FORM_INPUT_RADIO:
            case FormComponentType.FORM_INPUT_PHONE:
            case FormComponentType.FORM_INPUT_EMAIL:
            case FormComponentType.FORM_INPUT_CHECKBOX:
            case FormComponentType.FORM_INPUT_TEXT:
                return "varchar(500)";
            case FormComponentType.FORM_INPUT_NUMBER:
                return "double";
            case FormComponentType.FORM_INPUT_MONEY:
                return "decimal";
            case FormComponentType.FORM_INPUT_DATE:
                return "timestamp";
            case FormComponentType.FORM_INPUT_SWITCH:
                return "boolean";
        }

        throw new IsxAppException("不支持该组件类型");
    }

    /**
     * 将sql中每一个问号进行复制
     */
    public void SetReqValueToSecurityColumns(Map<String, FormComponentDto> componentMap,
        List<SecurityColumnDto> securityColumns, Map<String, Object> data) {

        // 按顺序遍历字段
        securityColumns.forEach(e -> {
            String columnName = e.getName().substring(3);
            // 获取当前组件信息
            FormComponentDto componentDto = componentMap.get(columnName);
            if (componentDto != null) {
                // 赋予值
                e.setValue(transComponentValue(componentDto, data.get(columnName)));
                // 赋予类型
                e.setType(transComponentType(componentDto.getComponentType()));
            }
        });
    }

    public void transComponentShowValue(FormComponentDto formComponent, String uuid, Map<String, Object> metaData,
        ResultSet resultSet, int columnIndex) throws SQLException {

        // 如果是sy_id翻译不走组件
        if ("sy_id".equalsIgnoreCase(uuid)) {
            metaData.put("sy_id", resultSet.getString(columnIndex));
            return;
        }

        // 如果是null值，直接跳过
        if (resultSet.getObject(columnIndex) == null || "null".equals(resultSet.getString(columnIndex))) {
            metaData.put(uuid, null);
            return;
        }

        // 对组件进行翻译
        switch (formComponent.getComponentType()) {
            case FormComponentType.FORM_INPUT_DATE:
                // 时间组件
                LocalDate date =
                    LocalDate.ofEpochDay(resultSet.getTimestamp(columnIndex).getTime() / (24 * 60 * 60 * 1000));
                if ("date".equals(formComponent.getDateType())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    metaData.put(uuid, date.format(formatter));
                    break;
                }
                if ("month".equals(formComponent.getDateType())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                    metaData.put(uuid, date.format(formatter));
                    break;
                }
                if ("year".equals(formComponent.getDateType())) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
                    metaData.put(uuid, date.format(formatter));
                    break;
                }
            case FormComponentType.FORM_INPUT_SWITCH:
                // 开关组件
                if (resultSet.getBoolean(columnIndex)) {
                    metaData.put(uuid, FormOptionValue.builder().label(formComponent.getSwitchInfo().getOpen())
                        .booleanValue(true).value("open").build());
                } else {
                    metaData.put(uuid, FormOptionValue.builder().label(formComponent.getSwitchInfo().getClose())
                        .booleanValue(false).value("close").build());
                }
                break;
            case FormComponentType.FORM_INPUT_SELECT:
            case FormComponentType.FORM_INPUT_RADIO:
                // 选择组件
                Map<String, FormOptionValue> optionsMap = new HashMap<>();
                formComponent.getOptions().forEach(e -> optionsMap.put(e.getValue(),
                    FormOptionValue.builder().label(e.getLabel()).value(e.getValue()).build()));
                metaData.put(uuid, optionsMap.get(resultSet.getString(columnIndex)));
                break;
            case FormComponentType.FORM_INPUT_CHECKBOX:
                List<String> values = JSON.parseArray(resultSet.getString(columnIndex), String.class);
                if (values.isEmpty()) {
                    metaData.put(uuid, new ArrayList<>());
                } else {
                    Map<String, FormOptionValue> optionsCheckboxMap = new HashMap<>();
                    formComponent.getOptions().forEach(e -> optionsCheckboxMap.put(e.getValue(),
                        FormOptionValue.builder().label(e.getLabel()).value(e.getValue()).build()));
                    List<FormOptionValue> valueList = new ArrayList<>();
                    values.forEach(e -> valueList.add(optionsCheckboxMap.get(e)));
                    metaData.put(uuid, valueList);
                }
                break;
            default:
                metaData.put(uuid, resultSet.getObject(columnIndex));
        }
    }
}
