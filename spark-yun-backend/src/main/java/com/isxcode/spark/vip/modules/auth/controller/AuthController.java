package com.isxcode.spark.vip.modules.auth.controller;

import com.isxcode.spark.api.auth.req.*;
import com.isxcode.spark.api.auth.res.PageSsoAuthRes;
import com.isxcode.spark.api.auth.res.QuerySsoAuthRes;
import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.api.auth.req.SsoLoginReq;
import com.isxcode.spark.api.user.res.LoginRes;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.auth.service.AuthBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "单点登录模块")
@RestController
@RequestMapping(ModuleVipCode.VIP_SSO_AUTH)
@RequiredArgsConstructor
public class AuthController {

    private final AuthBizService authBizService;

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "创建单点登录配置")
    @PostMapping("/addSsoAuth")
    @SuccessResponse("添加成功")
    public void addSsoAuth(@Valid @RequestBody AddSsoAuthReq addSsoAuthReq) {

        authBizService.addSsoAuth(addSsoAuthReq);
    }

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "更新单点登录配置")
    @PostMapping("/updateSsoAuth")
    @SuccessResponse("更新成功")
    public void updateSsoAuth(@Valid @RequestBody UpdateSsoAuthReq updateSsoAuthReq) {

        authBizService.updateSsoAuth(updateSsoAuthReq);
    }

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "启用单点登录接口")
    @PostMapping("/enableSsoAuth")
    @SuccessResponse("启动成功")
    public void enableSsoAuth(@Valid @RequestBody EnableSsoAuthReq enableSsoAuthReq) {

        authBizService.enableSsoAuth(enableSsoAuthReq);
    }

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "禁用单点登录接口")
    @PostMapping("/disableSsoAuth")
    @SuccessResponse("下线成功")
    public void disableSsoAuth(@Valid @RequestBody DisableSsoAuthReq disableSsoAuthReq) {

        authBizService.disableSsoAuth(disableSsoAuthReq);
    }

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "删除单点登录接口")
    @PostMapping("/deleteSsoAuth")
    @SuccessResponse("删除成功")
    public void deleteSsoAuth(@Valid @RequestBody DeleteSsoAuthReq deleteSsoAuthReq) {

        authBizService.deleteSsoAuth(deleteSsoAuthReq);
    }

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "分页查询单点登录配置接口")
    @PostMapping("/pageSsoAuth")
    @SuccessResponse("查询成功")
    public Page<PageSsoAuthRes> pageSsoAuth(@Valid @RequestBody PageSsoAuthReq pageSsoAuthReq) {

        return authBizService.pageSsoAuth(pageSsoAuthReq);
    }

    @Operation(summary = "单点认证")
    @PostMapping("/open/ssoLogin")
    @SuccessResponse("登录成功")
    public LoginRes ssoLogin(@Valid @RequestBody SsoLoginReq ssoLoginReq) {

        return authBizService.ssoLogin(ssoLoginReq);
    }

    @Operation(summary = "单点认证")
    @GetMapping("/open/getSsoAuthCode")
    @SuccessResponse("回调成功")
    public void getSsoAuthCode(@RequestParam(required = false) Map<String, String> requestBody) {
        log.info("单点登录回调，获取code值:{}", requestBody);
    }

    @VipApi
    @Secured({RoleType.SYS_ADMIN})
    @Operation(summary = "一键复制调用接口")
    @PostMapping("/getSsoInvokeUrl")
    @SuccessResponse("复制成功")
    public GetSsoInvokeUrlRes getSsoInvokeUrl(@Valid @RequestBody GetSsoInvokeUrlReq getSsoInvokeUrlReq) {

        return authBizService.getSsoInvokeUrl(getSsoInvokeUrlReq);
    }

    @Operation(summary = "获取单点登录列表")
    @PostMapping("/open/querySsoAuth")
    @SuccessResponse("获取成功")
    public List<QuerySsoAuthRes> querySsoAuth() {

        return authBizService.querySsoAuth();
    }
}
