package com.isxcode.spark.vip.modules.view.controller;

import com.isxcode.spark.api.main.constants.ModuleVipCode;
import com.isxcode.spark.api.user.constants.RoleType;
import com.isxcode.spark.api.view.req.*;
import com.isxcode.spark.api.view.res.*;
import com.isxcode.spark.common.annotations.successResponse.SuccessResponse;
import com.isxcode.spark.vip.annotation.vip.VipApi;
import com.isxcode.spark.vip.modules.view.service.ViewBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@Tag(name = "数据大屏模块")
@RequestMapping(ModuleVipCode.VIP_VIEW)
@RestController
@RequiredArgsConstructor
public class ViewController {

    private final ViewBizService viewBizService;

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "创建卡片接口")
    @PostMapping("/addViewCard")
    @SuccessResponse("添加成功")
    public GetViewCardRes addViewCard(@Valid @RequestBody AddViewCardReq addViewCardReq) {

        return viewBizService.addViewCard(addViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询卡片接口")
    @PostMapping("/pageViewCard")
    @SuccessResponse("查询成功")
    public Page<PageViewCardRes> pageViewCard(@Valid @RequestBody PageViewCardReq pageViewCardReq) {

        return viewBizService.pageViewCard(pageViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_ADMIN})
    @Operation(summary = "删除卡片接口")
    @PostMapping("/deleteViewCard")
    @SuccessResponse("删除成功")
    public void deleteViewCard(@Valid @RequestBody DeleteViewCardReq deleteViewCardReq) {

        viewBizService.deleteViewCard(deleteViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "配置卡片接口")
    @PostMapping("/configViewCard")
    @SuccessResponse("配置成功")
    public void configViewCard(@Valid @RequestBody ConfigViewCardReq configViewCardReq) {

        viewBizService.configViewCard(configViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "查询卡片接口")
    @PostMapping("/getViewCard")
    @SuccessResponse("查询成功")
    public GetViewCardRes getViewCard(@Valid @RequestBody GetViewCardReq getViewCardReq) {

        return viewBizService.getViewCard(getViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "发布卡片接口")
    @PostMapping("/publishViewCard")
    @SuccessResponse("发布成功")
    public void publishViewCard(@Valid @RequestBody PublishViewCardReq publishViewCardReq) {

        viewBizService.publishViewCard(publishViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线卡片接口")
    @PostMapping("/offlineViewCard")
    @SuccessResponse("下线成功")
    public void offlineViewCard(@Valid @RequestBody OfflineViewCardReq offlineViewCardReq) {

        viewBizService.offlineViewCard(offlineViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "刷新卡片数据接口")
    @PostMapping("/getViewCardData")
    @SuccessResponse("获取成功")
    public GetViewCardDataRes getViewCardData(@Valid @RequestBody GetViewCardDataReq getViewCardDataReq) {

        return viewBizService.getViewCardData(getViewCardDataReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "创建大屏接口")
    @PostMapping("/addView")
    @SuccessResponse("添加成功")
    public AddViewRes addView(@Valid @RequestBody AddViewReq addViewReq) {

        return viewBizService.addView(addViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "分页查询大屏接口")
    @PostMapping("/pageView")
    @SuccessResponse("查询成功")
    public Page<PageViewRes> pageView(@Valid @RequestBody PageViewReq pageViewReq) {

        return viewBizService.pageView(pageViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_ADMIN})
    @Operation(summary = "删除大屏接口")
    @PostMapping("/deleteView")
    @SuccessResponse("删除成功")
    public void deleteView(@Valid @RequestBody DeleteViewReq deleteViewReq) {

        viewBizService.deleteView(deleteViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "发布大屏接口")
    @PostMapping("/publishView")
    @SuccessResponse("发布成功")
    public void publishView(@Valid @RequestBody PublishViewReq publishViewReq) {

        viewBizService.publishView(publishViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "下线大屏接口")
    @PostMapping("/offlineView")
    @SuccessResponse("下线成功")
    public void offlineView(@Valid @RequestBody OfflineViewReq offlineViewReq) {

        viewBizService.offlineView(offlineViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "配置大屏接口")
    @PostMapping("/configView")
    @SuccessResponse("保存成功")
    public void configView(@Valid @RequestBody ConfigViewReq configViewReq) {

        viewBizService.configView(configViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN, RoleType.ROLE_ANONYMOUS})
    @Operation(summary = "查询大屏信息接口")
    @PostMapping("/getView")
    @SuccessResponse("查询成功")
    public GetViewRes getView(@Valid @RequestBody GetViewReq getViewReq) {

        return viewBizService.getView(getViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN, RoleType.ROLE_ANONYMOUS})
    @Operation(summary = "查询卡片id查询卡片数据接口")
    @PostMapping("/getViewCardDataById")
    @SuccessResponse("查询成功")
    public GetViewCardDataByIdRes getViewCardDataById(
        @Valid @RequestBody GetViewCardDataByIdReq getViewCardDataByIdReq) {

        return viewBizService.getViewCardDataById(getViewCardDataByIdReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "sql预览接口")
    @PostMapping("/getSqlData")
    @SuccessResponse("查询成功")
    public GetSqlDataRes getSqlData(@Valid @RequestBody GetSqlDataReq addViewReq) {

        return viewBizService.getSqlData(addViewReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "获取分享大屏链接的id接口")
    @PostMapping("/getViewLink")
    @SuccessResponse("获取成功")
    public GetViewLinkRes getViewLink(@Valid @RequestBody GetViewLinkReq getViewLinkReq) {

        return viewBizService.getViewLink(getViewLinkReq);
    }

    @VipApi
    @Operation(summary = "获取分享大屏链接的id接口")
    @PostMapping("/open/getViewLinkInfo")
    @SuccessResponse("获取成功")
    public GetViewLinkInfoRes getViewLinkInfo(@Valid @RequestBody GetViewLinkInfoReq getViewLinkInfoReq) {

        return viewBizService.getViewLinkInfo(getViewLinkInfoReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "编辑卡片信息接口")
    @PostMapping("/editViewCard")
    @SuccessResponse("修改成功")
    public void editViewCard(@Valid @RequestBody EditViewCardReq editViewCardReq) {

        viewBizService.editViewCard(editViewCardReq);
    }

    @VipApi
    @Secured({RoleType.TENANT_MEMBER, RoleType.TENANT_ADMIN})
    @Operation(summary = "编辑大屏接口")
    @PostMapping("/editView")
    @SuccessResponse("修改成功")
    public void editView(@Valid @RequestBody EditViewReq editViewReq) {

        viewBizService.editView(editViewReq);
    }
}
